<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas 2D渲染WebGL数据演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #4CAF50;
        }
        
        .canvas-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .canvas-wrapper {
            text-align: center;
        }
        
        .canvas-wrapper h3 {
            margin: 10px 0;
            color: #ccc;
        }
        
        canvas {
            border: 1px solid #333;
            background: #000;
            border-radius: 4px;
        }
        
        .controls {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        button {
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .data-display {
            background: #444;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas 2D渲染WebGL数据演示</h1>
        
        <div class="canvas-container">
            <div class="canvas-wrapper">
                <h3>WebGL渲染</h3>
                <canvas id="webglCanvas" width="400" height="300"></canvas>
            </div>
            <div class="canvas-wrapper">
                <h3>Canvas 2D渲染</h3>
                <canvas id="canvas2d" width="400" height="300"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <h3>测试案例</h3>
            <div class="button-group">
                <button onclick="testTriangle()">基础三角形</button>
                <button onclick="testQuad()">四边形</button>
                <button onclick="testColoredTriangles()">彩色三角形</button>
                <button onclick="testTexturedQuad()">纹理四边形</button>
                <button onclick="testMSDFText()">MSDF文本数据</button>
                <button onclick="testWireframe()">线框模式</button>
                <button onclick="clearCanvas()">清除画布</button>
            </div>
        </div>
        
        <div class="info">
            <h3>当前数据</h3>
            <div>顶点数据:</div>
            <div class="data-display" id="verticesDisplay">无数据</div>
            <div>索引数据:</div>
            <div class="data-display" id="indicesDisplay">无数据</div>
        </div>
        
        <div class="info">
            <h3>功能说明</h3>
            <ul>
                <li><strong>drawInCanvas2DByGlData(ctx, vertices, indices)</strong> - 主要渲染函数</li>
                <li>自动检测顶点格式：位置、纹理坐标、颜色</li>
                <li>支持纹理映射和顶点颜色</li>
                <li>支持线框模式和顶点显示</li>
                <li>兼容WebGL的Float32Array和Uint16Array数据格式</li>
            </ul>
        </div>
    </div>

    <script type="module">
        import { drawInCanvas2DByGlData, drawWireframe, drawVertices, createTransform } from './canvas2d-gl-renderer.js';
        
        // 获取画布上下文
        const canvas2d = document.getElementById('canvas2d');
        const ctx2d = canvas2d.getContext('2d');
        const webglCanvas = document.getElementById('webglCanvas');
        const webglCtx = webglCanvas.getContext('2d'); // 用2D上下文模拟WebGL显示
        
        let currentVertices = null;
        let currentIndices = null;
        
        // 更新数据显示
        function updateDataDisplay(vertices, indices) {
            currentVertices = vertices;
            currentIndices = indices;
            
            document.getElementById('verticesDisplay').textContent = 
                `Float32Array(${vertices.length}): [${Array.from(vertices).map(v => v.toFixed(2)).join(', ')}]`;
            document.getElementById('indicesDisplay').textContent = 
                `Uint16Array(${indices.length}): [${Array.from(indices).join(', ')}]`;
        }
        
        // 清除画布
        function clearCanvas() {
            ctx2d.clearRect(0, 0, canvas2d.width, canvas2d.height);
            webglCtx.clearRect(0, 0, webglCanvas.width, webglCanvas.height);
        }
        
        // 在WebGL画布上绘制参考图形（用2D模拟）
        function drawReference(vertices, indices, options = {}) {
            webglCtx.save();
            webglCtx.fillStyle = options.fillStyle || '#4CAF50';
            webglCtx.strokeStyle = options.strokeStyle || '#fff';
            webglCtx.lineWidth = 2;
            
            // 绘制三角形
            for (let i = 0; i < indices.length; i += 3) {
                const i1 = indices[i] * 4;
                const i2 = indices[i + 1] * 4;
                const i3 = indices[i + 2] * 4;
                
                webglCtx.beginPath();
                webglCtx.moveTo(vertices[i1], vertices[i1 + 1]);
                webglCtx.lineTo(vertices[i2], vertices[i2 + 1]);
                webglCtx.lineTo(vertices[i3], vertices[i3 + 1]);
                webglCtx.closePath();
                
                if (!options.wireframe) {
                    webglCtx.fill();
                }
                webglCtx.stroke();
            }
            
            webglCtx.restore();
        }
        
        // 测试基础三角形
        function testTriangle() {
            clearCanvas();
            
            // 顶点数据: x, y, u, v
            const vertices = new Float32Array([
                100, 50,  0.0, 0.0,  // 顶点0
                200, 200, 1.0, 0.0,  // 顶点1
                50,  200, 0.0, 1.0   // 顶点2
            ]);
            
            const indices = new Uint16Array([0, 1, 2]);
            
            updateDataDisplay(vertices, indices);
            
            // Canvas 2D渲染
            drawInCanvas2DByGlData(ctx2d, vertices, indices, {
                fillStyle: '#ff6b6b',
                strokeStyle: '#fff',
                lineWidth: 2
            });
            
            // 参考渲染
            drawReference(vertices, indices, { fillStyle: '#4CAF50' });
        }
        
        // 测试四边形
        function testQuad() {
            clearCanvas();
            
            // 四边形顶点数据
            const vertices = new Float32Array([
                50,  50,  0.0, 0.0,  // 左上
                250, 50,  1.0, 0.0,  // 右上
                250, 200, 1.0, 1.0,  // 右下
                50,  200, 0.0, 1.0   // 左下
            ]);
            
            // 两个三角形组成四边形
            const indices = new Uint16Array([
                0, 1, 2,  // 第一个三角形
                0, 2, 3   // 第二个三角形
            ]);
            
            updateDataDisplay(vertices, indices);
            
            // Canvas 2D渲染
            drawInCanvas2DByGlData(ctx2d, vertices, indices, {
                fillStyle: '#4ecdc4',
                strokeStyle: '#fff',
                lineWidth: 1
            });
            
            // 参考渲染
            drawReference(vertices, indices, { fillStyle: '#4CAF50' });
        }
        
        // 测试彩色三角形
        function testColoredTriangles() {
            clearCanvas();
            
            // 顶点数据: x, y, r, g, b, a
            const vertices = new Float32Array([
                // 第一个三角形 - 红色
                100, 50,  1.0, 0.0, 0.0, 1.0,
                200, 150, 1.0, 0.0, 0.0, 1.0,
                50,  150, 1.0, 0.0, 0.0, 1.0,
                
                // 第二个三角形 - 绿色
                200, 50,  0.0, 1.0, 0.0, 1.0,
                300, 150, 0.0, 1.0, 0.0, 1.0,
                150, 150, 0.0, 1.0, 0.0, 1.0,
                
                // 第三个三角形 - 蓝色
                150, 150, 0.0, 0.0, 1.0, 1.0,
                250, 250, 0.0, 0.0, 1.0, 1.0,
                100, 250, 0.0, 0.0, 1.0, 1.0
            ]);
            
            const indices = new Uint16Array([
                0, 1, 2,  // 红色三角形
                3, 4, 5,  // 绿色三角形
                6, 7, 8   // 蓝色三角形
            ]);
            
            updateDataDisplay(vertices, indices);
            
            // Canvas 2D渲染（使用顶点颜色）
            drawInCanvas2DByGlData(ctx2d, vertices, indices, {
                strokeStyle: '#fff',
                lineWidth: 1
            });
            
            // 参考渲染
            webglCtx.fillStyle = '#ff0000';
            drawReference(vertices.slice(0, 18), new Uint16Array([0, 1, 2]), { fillStyle: '#ff0000' });
            webglCtx.fillStyle = '#00ff00';
            drawReference(vertices.slice(18, 36), new Uint16Array([0, 1, 2]), { fillStyle: '#00ff00' });
            webglCtx.fillStyle = '#0000ff';
            drawReference(vertices.slice(36, 54), new Uint16Array([0, 1, 2]), { fillStyle: '#0000ff' });
        }
        
        // 测试纹理四边形
        function testTexturedQuad() {
            clearCanvas();
            
            // 创建简单的纹理
            const textureCanvas = document.createElement('canvas');
            textureCanvas.width = 64;
            textureCanvas.height = 64;
            const textureCtx = textureCanvas.getContext('2d');
            
            // 绘制棋盘纹理
            for (let x = 0; x < 64; x += 8) {
                for (let y = 0; y < 64; y += 8) {
                    textureCtx.fillStyle = ((x + y) / 8) % 2 ? '#fff' : '#000';
                    textureCtx.fillRect(x, y, 8, 8);
                }
            }
            
            // 四边形顶点数据 (x, y, u, v)
            const vertices = new Float32Array([
                100, 50,  0.0, 0.0,  // 左上
                300, 50,  1.0, 0.0,  // 右上
                300, 250, 1.0, 1.0,  // 右下
                100, 250, 0.0, 1.0   // 左下
            ]);
            
            const indices = new Uint16Array([
                0, 1, 2,
                0, 2, 3
            ]);
            
            updateDataDisplay(vertices, indices);
            
            // Canvas 2D渲染（带纹理）
            drawInCanvas2DByGlData(ctx2d, vertices, indices, {
                texture: textureCanvas,
                strokeStyle: '#fff',
                lineWidth: 2
            });
            
            // 参考渲染
            webglCtx.drawImage(textureCanvas, 100, 50, 200, 200);
            webglCtx.strokeStyle = '#4CAF50';
            webglCtx.lineWidth = 2;
            webglCtx.strokeRect(100, 50, 200, 200);
        }
        
        // 测试MSDF文本数据
        function testMSDFText() {
            clearCanvas();
            
            // 模拟MSDF文本的顶点数据（简化版）
            const vertices = new Float32Array([
                // 字符1 - 四边形
                50,  100, 0.0, 0.0,
                100, 100, 0.25, 0.0,
                100, 150, 0.25, 1.0,
                50,  150, 0.0, 1.0,
                
                // 字符2 - 四边形
                120, 100, 0.25, 0.0,
                170, 100, 0.5, 0.0,
                170, 150, 0.5, 1.0,
                120, 150, 0.25, 1.0,
                
                // 字符3 - 四边形
                190, 100, 0.5, 0.0,
                240, 100, 0.75, 0.0,
                240, 150, 0.75, 1.0,
                190, 150, 0.5, 1.0
            ]);
            
            const indices = new Uint16Array([
                // 字符1
                0, 1, 2, 0, 2, 3,
                // 字符2
                4, 5, 6, 4, 6, 7,
                // 字符3
                8, 9, 10, 8, 10, 11
            ]);
            
            updateDataDisplay(vertices, indices);
            
            // Canvas 2D渲染
            drawInCanvas2DByGlData(ctx2d, vertices, indices, {
                fillStyle: '#ffd93d',
                strokeStyle: '#ff6b35',
                lineWidth: 2
            });
            
            // 参考渲染
            drawReference(vertices, indices, { fillStyle: '#4CAF50' });
        }
        
        // 测试线框模式
        function testWireframe() {
            if (!currentVertices || !currentIndices) {
                testQuad(); // 先生成一些数据
            }
            
            clearCanvas();
            
            // 绘制线框
            drawWireframe(ctx2d, currentVertices, currentIndices, {
                strokeStyle: '#00ff00',
                lineWidth: 2
            });
            
            // 绘制顶点
            drawVertices(ctx2d, currentVertices, {
                radius: 4,
                fillStyle: '#ff0000'
            });
            
            // 参考渲染
            drawReference(currentVertices, currentIndices, { 
                wireframe: true, 
                strokeStyle: '#4CAF50' 
            });
        }
        
        // 全局函数
        window.testTriangle = testTriangle;
        window.testQuad = testQuad;
        window.testColoredTriangles = testColoredTriangles;
        window.testTexturedQuad = testTexturedQuad;
        window.testMSDFText = testMSDFText;
        window.testWireframe = testWireframe;
        window.clearCanvas = clearCanvas;
        
        // 初始演示
        testTriangle();
    </script>
</body>
</html>
