<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视口矩阵限制演示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            overflow: hidden;
        }
        
        canvas {
            display: block;
            background: white;
            cursor: grab;
        }
        
        canvas:active {
            cursor: grabbing;
        }
        
        .controls {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .controls h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .controls button:hover {
            background: #0056b3;
        }
        
        .controls .slider-group {
            margin: 10px 0;
        }
        
        .controls label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-size: 12px;
        }
        
        .controls input[type="range"] {
            width: 200px;
        }
        
        .info {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            z-index: 100;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 8px;
            max-width: 300px;
            font-size: 14px;
            color: #333;
        }
        
        .instructions h4 {
            margin: 0 0 10px 0;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <canvas id="viewport-canvas"></canvas>
    
    <div class="controls">
        <h3>视口控制</h3>
        <button onclick="resetView()">重置视图</button>
        <button onclick="fitContent()">适应内容</button>
        
        <div class="slider-group">
            <label>最小缩放: <span id="min-scale-value">0.1</span></label>
            <input type="range" id="min-scale" min="0.05" max="1" step="0.05" value="0.1" 
                   onchange="updateConstraints()">
        </div>
        
        <div class="slider-group">
            <label>最大缩放: <span id="max-scale-value">5</span></label>
            <input type="range" id="max-scale" min="1" max="10" step="0.5" value="5" 
                   onchange="updateConstraints()">
        </div>
        
        <div class="slider-group">
            <label>可见比例: <span id="visible-ratio-value">0.2</span></label>
            <input type="range" id="visible-ratio" min="-0.1" max="1" step="0.1" value="0.2" 
                   onchange="updateConstraints()">
        </div>
        
        <button onclick="toggleAnimation()">切换动画</button>
    </div>
    
    <div class="info" id="info-panel">
        <div>缩放: 1.00x, 1.00x</div>
        <div>平移: 0, 0</div>
        <div>动画: 无</div>
        <div>约束: 活跃</div>
    </div>
    
    <div class="instructions">
        <h4>操作说明</h4>
        <ul>
            <li>鼠标滚轮：缩放</li>
            <li>鼠标拖拽：平移</li>
            <li>调整滑块：修改约束参数</li>
            <li>可见比例 -0.1：无限制</li>
            <li>可见比例 0：始终有内容</li>
            <li>可见比例 1：内容完全可见</li>
        </ul>
    </div>

    <script type="module">
        // 简化的矩阵工具类（用于演示）
        class SimpleMatrixUtils {
            static identity() {
                return [1, 0, 0, 1, 0, 0];
            }
            
            static multiply(m1, m2) {
                const [a1, b1, c1, d1, tx1, ty1] = m1;
                const [a2, b2, c2, d2, tx2, ty2] = m2;
                return [
                    a1 * a2 + b1 * c2,
                    a1 * b2 + b1 * d2,
                    c1 * a2 + d1 * c2,
                    c1 * b2 + d1 * d2,
                    tx1 * a2 + ty1 * c2 + tx2,
                    tx1 * b2 + ty1 * d2 + ty2
                ];
            }
            
            static scale(sx, sy = sx) {
                return [sx, 0, 0, sy, 0, 0];
            }
            
            static translate(tx, ty) {
                return [1, 0, 0, 1, tx, ty];
            }
            
            static getScale(matrix) {
                const [a, b, c, d] = matrix;
                return {
                    x: Math.sqrt(a * a + b * b),
                    y: Math.sqrt(c * c + d * d)
                };
            }
            
            static getTranslation(matrix) {
                return { x: matrix[4], y: matrix[5] };
            }
            
            static invert(matrix) {
                const [a, b, c, d, tx, ty] = matrix;
                const det = a * d - b * c;
                if (Math.abs(det) < 1e-10) return null;
                const invDet = 1 / det;
                return [
                    d * invDet,
                    -b * invDet,
                    -c * invDet,
                    a * invDet,
                    (c * ty - d * tx) * invDet,
                    (b * tx - a * ty) * invDet
                ];
            }
            
            static transformPoint(point, matrix) {
                const [a, b, c, d, tx, ty] = matrix;
                return {
                    x: a * point.x + c * point.y + tx,
                    y: b * point.x + d * point.y + ty
                };
            }
        }

        // 简化的视口管理器
        class SimpleViewportManager {
            constructor(canvas) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.currentMatrix = SimpleMatrixUtils.identity();
                this.contentRect = { x: 0, y: 0, width: 1200, height: 800 };
                this.constraints = {
                    minScale: 0.1,
                    maxScale: 5,
                    keepVisibleProportion: 0.2
                };
                this.animationEnabled = true;
                
                this.bindEvents();
                this.updateCanvasSize();
                this.render();
            }
            
            bindEvents() {
                // 滚轮缩放
                this.canvas.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    const rect = this.canvas.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
                    this.zoomAt(x, y, scaleFactor);
                });

                // 拖拽平移
                let isDragging = false;
                let lastX = 0, lastY = 0;

                this.canvas.addEventListener('mousedown', (e) => {
                    isDragging = true;
                    lastX = e.clientX;
                    lastY = e.clientY;
                });

                this.canvas.addEventListener('mousemove', (e) => {
                    if (isDragging) {
                        const deltaX = e.clientX - lastX;
                        const deltaY = e.clientY - lastY;
                        this.pan(deltaX, deltaY);
                        lastX = e.clientX;
                        lastY = e.clientY;
                    }
                });

                this.canvas.addEventListener('mouseup', () => {
                    isDragging = false;
                });

                window.addEventListener('resize', () => {
                    this.updateCanvasSize();
                });
            }
            
            zoomAt(x, y, scaleFactor) {
                const invMatrix = SimpleMatrixUtils.invert(this.currentMatrix);
                if (!invMatrix) return;
                
                const worldPoint = SimpleMatrixUtils.transformPoint({ x, y }, invMatrix);
                const scaleMatrix = SimpleMatrixUtils.scale(scaleFactor);
                let newMatrix = SimpleMatrixUtils.multiply(this.currentMatrix, scaleMatrix);
                
                const newInvMatrix = SimpleMatrixUtils.invert(newMatrix);
                if (!newInvMatrix) return;
                
                const newWorldPoint = SimpleMatrixUtils.transformPoint({ x, y }, newInvMatrix);
                const scale = SimpleMatrixUtils.getScale(newMatrix);
                const deltaX = (worldPoint.x - newWorldPoint.x) * scale.x;
                const deltaY = (worldPoint.y - newWorldPoint.y) * scale.y;
                
                newMatrix[4] += deltaX;
                newMatrix[5] += deltaY;
                
                this.setMatrix(newMatrix);
            }
            
            pan(deltaX, deltaY) {
                const newMatrix = [...this.currentMatrix];
                newMatrix[4] += deltaX;
                newMatrix[5] += deltaY;
                this.setMatrix(newMatrix);
            }
            
            setMatrix(matrix) {
                const constrainedMatrix = this.constrainMatrix(matrix);
                this.currentMatrix = constrainedMatrix;
                this.render();
                this.updateInfo();
            }
            
            constrainMatrix(matrix) {
                if (this.constraints.keepVisibleProportion < 0) {
                    return matrix;
                }
                
                let result = [...matrix];
                
                // 限制缩放
                const scale = SimpleMatrixUtils.getScale(result);
                const clampedScaleX = Math.max(this.constraints.minScale, 
                                              Math.min(this.constraints.maxScale, scale.x));
                const clampedScaleY = Math.max(this.constraints.minScale, 
                                              Math.min(this.constraints.maxScale, scale.y));
                
                if (Math.abs(scale.x - clampedScaleX) > 1e-6) {
                    const scaleFactor = clampedScaleX / scale.x;
                    result[0] *= scaleFactor;
                    result[1] *= scaleFactor;
                    result[2] *= scaleFactor;
                    result[3] *= scaleFactor;
                }
                
                // 简化的平移限制
                const translation = SimpleMatrixUtils.getTranslation(result);
                const currentScale = SimpleMatrixUtils.getScale(result);
                const scaledContentWidth = this.contentRect.width * currentScale.x;
                const scaledContentHeight = this.contentRect.height * currentScale.y;
                
                let minX, maxX, minY, maxY;
                
                if (this.constraints.keepVisibleProportion === 1) {
                    // 完全可见
                    minX = 0;
                    maxX = this.canvas.width - scaledContentWidth;
                    minY = 0;
                    maxY = this.canvas.height - scaledContentHeight;
                } else if (this.constraints.keepVisibleProportion === 0) {
                    // 始终有内容
                    minX = this.canvas.width - scaledContentWidth;
                    maxX = 0;
                    minY = this.canvas.height - scaledContentHeight;
                    maxY = 0;
                } else {
                    // 按比例
                    const margin = Math.min(scaledContentWidth, scaledContentHeight) * 
                                  (1 - this.constraints.keepVisibleProportion);
                    minX = -margin;
                    maxX = this.canvas.width - scaledContentWidth + margin;
                    minY = -margin;
                    maxY = this.canvas.height - scaledContentHeight + margin;
                }
                
                result[4] = Math.max(minX, Math.min(maxX, translation.x));
                result[5] = Math.max(minY, Math.min(maxY, translation.y));
                
                return result;
            }
            
            updateCanvasSize() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
                this.render();
            }
            
            render() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                
                this.ctx.save();
                const [a, b, c, d, tx, ty] = this.currentMatrix;
                this.ctx.setTransform(a, b, c, d, tx, ty);
                
                this.drawContent();
                this.ctx.restore();
            }
            
            drawContent() {
                // 绘制内容区域
                this.ctx.strokeStyle = '#333';
                this.ctx.lineWidth = 3;
                this.ctx.strokeRect(
                    this.contentRect.x,
                    this.contentRect.y,
                    this.contentRect.width,
                    this.contentRect.height
                );
                
                // 绘制网格
                this.ctx.strokeStyle = '#ddd';
                this.ctx.lineWidth = 1;
                const gridSize = 50;
                
                for (let x = 0; x <= this.contentRect.width; x += gridSize) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, 0);
                    this.ctx.lineTo(x, this.contentRect.height);
                    this.ctx.stroke();
                }
                
                for (let y = 0; y <= this.contentRect.height; y += gridSize) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(this.contentRect.width, y);
                    this.ctx.stroke();
                }
                
                // 绘制示例对象
                this.ctx.fillStyle = '#ff6b6b';
                this.ctx.fillRect(100, 100, 150, 150);
                
                this.ctx.fillStyle = '#4ecdc4';
                this.ctx.fillRect(400, 200, 200, 100);
                
                this.ctx.fillStyle = '#45b7d1';
                this.ctx.fillRect(700, 150, 100, 200);
                
                this.ctx.fillStyle = '#f9ca24';
                this.ctx.fillRect(300, 500, 300, 80);
                
                // 绘制中心点
                this.ctx.fillStyle = '#e74c3c';
                this.ctx.beginPath();
                this.ctx.arc(this.contentRect.width / 2, this.contentRect.height / 2, 10, 0, Math.PI * 2);
                this.ctx.fill();
            }
            
            updateInfo() {
                const scale = SimpleMatrixUtils.getScale(this.currentMatrix);
                const translation = SimpleMatrixUtils.getTranslation(this.currentMatrix);
                
                document.getElementById('info-panel').innerHTML = `
                    <div>缩放: ${scale.x.toFixed(2)}x, ${scale.y.toFixed(2)}x</div>
                    <div>平移: ${translation.x.toFixed(0)}, ${translation.y.toFixed(0)}</div>
                    <div>动画: ${this.animationEnabled ? '启用' : '禁用'}</div>
                    <div>约束: 活跃</div>
                `;
            }
            
            resetView() {
                this.setMatrix(SimpleMatrixUtils.identity());
            }
            
            fitContent() {
                const scaleX = this.canvas.width / this.contentRect.width;
                const scaleY = this.canvas.height / this.contentRect.height;
                const scale = Math.min(scaleX, scaleY) * 0.9;
                
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;
                const contentCenterX = this.contentRect.width / 2;
                const contentCenterY = this.contentRect.height / 2;
                
                const matrix = SimpleMatrixUtils.multiply(
                    SimpleMatrixUtils.translate(centerX - contentCenterX * scale, centerY - contentCenterY * scale),
                    SimpleMatrixUtils.scale(scale)
                );
                
                this.setMatrix(matrix);
            }
            
            updateConstraints(constraints) {
                this.constraints = { ...this.constraints, ...constraints };
                this.setMatrix(this.currentMatrix); // 重新应用约束
            }
        }

        // 初始化
        const canvas = document.getElementById('viewport-canvas');
        const manager = new SimpleViewportManager(canvas);

        // 全局函数
        window.resetView = () => manager.resetView();
        window.fitContent = () => manager.fitContent();
        window.toggleAnimation = () => {
            manager.animationEnabled = !manager.animationEnabled;
            manager.updateInfo();
        };
        
        window.updateConstraints = () => {
            const minScale = parseFloat(document.getElementById('min-scale').value);
            const maxScale = parseFloat(document.getElementById('max-scale').value);
            const visibleRatio = parseFloat(document.getElementById('visible-ratio').value);
            
            document.getElementById('min-scale-value').textContent = minScale;
            document.getElementById('max-scale-value').textContent = maxScale;
            document.getElementById('visible-ratio-value').textContent = visibleRatio;
            
            manager.updateConstraints({
                minScale,
                maxScale,
                keepVisibleProportion: visibleRatio
            });
        };
    </script>
</body>
</html>
