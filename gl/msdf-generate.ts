interface MSDFGlyph {
  char: string;
  data: Uint8ClampedArray;
  width: number;
  height: number;
  bearingX: number;
  bearingY: number;
  advance: number;
}

export class CanvasMSDFGenerator {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private size: number;

  constructor(size = 64) {
    this.size = size;
    this.canvas = document.createElement('canvas');
    this.canvas.width = size;
    this.canvas.height = size;
    this.ctx = this.canvas.getContext('2d', { willReadFrequently: true })!;
  }

  /**
   * 生成字符的 MSDF 数据
   */
  generateCharMSDF(char: string, font: string = `bold ${this.size * 0.8}px Arial`): MSDFGlyph {
    // 清空画布
    this.ctx.fillStyle = 'black';
    this.ctx.fillRect(0, 0, this.size, this.size);

    // 设置字体
    this.ctx.font = font;
    this.ctx.fillStyle = 'white';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';

    // 绘制字符
    this.ctx.fillText(char, this.size / 2, this.size / 2);

    // 获取二值图像数据
    const imageData = this.ctx.getImageData(0, 0, this.size, this.size);

    // 转换为 MSDF
    const msdfData = this.convertToMSDF(imageData);

    // 测量字符属性
    const metrics = this.measureChar(char, font);

    return {
      char,
      data: msdfData,
      width: this.size,
      height: this.size,
      bearingX: metrics.bearingX,
      bearingY: metrics.bearingY,
      advance: metrics.advance,
    };
  }

  /**
   * 生成形状的 MSDF 数据
   */
  generateShapeMSDF(drawFunction: (ctx: CanvasRenderingContext2D) => void): {
    data: Uint8ClampedArray;
    width: number;
    height: number;
  } {
    // 清空画布
    this.ctx.fillStyle = 'black';
    this.ctx.fillRect(0, 0, this.size, this.size);

    // 设置绘制样式
    this.ctx.fillStyle = 'white';

    // 执行绘制函数
    drawFunction(this.ctx);

    // 获取图像数据并转换
    const imageData = this.ctx.getImageData(0, 0, this.size, this.size);
    const msdfData = this.convertToMSDF(imageData);

    return {
      data: msdfData,
      width: this.size,
      height: this.size,
    };
  }

  /**
   * 将二值图像转换为 MSDF
   */
  private convertToMSDF(imageData: ImageData): Uint8ClampedArray {
    const { width, height, data } = imageData;
    const msdfData = new Uint8ClampedArray(width * height * 3); // RGB三通道

    // 计算距离场
    const distanceField = this.computeDistanceField(data, width, height);

    // 转换为多通道符号距离场
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 3;
        const dist = distanceField[y][x];

        // 简化的 MSDF - 实际应该计算不同方向的梯度
        // 这里用同一个距离值填充三个通道（简化实现）
        const normalized = Math.max(0, Math.min(255, (dist + 10) * 12.75));

        msdfData[idx] = normalized; // R
        msdfData[idx + 1] = normalized; // G
        msdfData[idx + 2] = normalized; // B
      }
    }

    return msdfData;
  }

  /**
   * 计算距离场
   */
  private computeDistanceField(data: Uint8ClampedArray, width: number, height: number): number[][] {
    const distanceField: number[][] = [];
    const maxSearch = Math.min(width, height) / 2;

    // 初始化距离场
    for (let y = 0; y < height; y++) {
      distanceField[y] = [];
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4;
        const alpha = data[idx + 3];
        const isInside = alpha > 128;

        // 找到到最近边缘的距离
        let minDistance = maxSearch;

        for (let dy = -maxSearch; dy <= maxSearch; dy++) {
          for (let dx = -maxSearch; dx <= maxSearch; dx++) {
            const nx = x + dx;
            const ny = y + dy;

            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
              const nIdx = (ny * width + nx) * 4;
              const nAlpha = data[nIdx + 3];
              const isEdge = isInside ? nAlpha <= 128 : nAlpha > 128;

              if (isEdge) {
                const distance = Math.sqrt(dx * dx + dy * dy);
                minDistance = Math.min(minDistance, distance);
              }
            }
          }
        }

        // 内部为负距离，外部为正距离
        distanceField[y][x] = isInside ? -minDistance : minDistance;
      }
    }

    return distanceField;
  }

  /**
   * 测量字符属性
   */
  private measureChar(char: string, font: string) {
    this.ctx.font = font;
    const metrics = this.ctx.measureText(char);

    return {
      bearingX: 0,
      bearingY: 0,
      advance: metrics.width,
    };
  }

  /**
   * 导出为 PNG 图片
   */
  exportAsPNG(msdfData: Uint8ClampedArray, width: number, height: number) {
    const exportCanvas = document.createElement('canvas');
    exportCanvas.width = width;
    exportCanvas.height = height;
    const exportCtx = exportCanvas.getContext('2d')!;

    const imageData = exportCtx.createImageData(width, height);
    const data = imageData.data;

    // 将 MSDF 数据转换为 RGBA
    for (let i = 0; i < msdfData.length; i += 3) {
      const pixelIdx = ((i / 3) * 4) | 0;
      data[pixelIdx] = msdfData[i]; // R
      data[pixelIdx + 1] = msdfData[i + 1]; // G
      data[pixelIdx + 2] = msdfData[i + 2]; // B
      data[pixelIdx + 3] = 255; // A
    }

    exportCtx.putImageData(imageData, 0, 0);
    return exportCanvas;

    // return new Promise((resolve) => {
    //   exportCanvas.toBlob(resolve as any, 'image/png');
    // });
  }
}
export class MSDFTextureAtlas {
  private generator: CanvasMSDFGenerator;
  private glyphs: Map<string, MSDFGlyph> = new Map();

  constructor(private canvasSize = 64) {
    this.generator = new CanvasMSDFGenerator(canvasSize);
  }

  /**
   * 添加字符到图集
   */
  async addChars(chars: string[], font?: string): Promise<void> {
    for (const char of chars) {
      const glyph = this.generator.generateCharMSDF(char, font);
      this.glyphs.set(char, glyph);
    }
  }

  /**
   * 添加形状到图集
   */
  async addShape(
    name: string,
    drawFunction: (ctx: CanvasRenderingContext2D) => void,
  ): Promise<void> {
    const result = this.generator.generateShapeMSDF(drawFunction);

    this.glyphs.set(name, {
      char: name,
      data: result.data,
      width: result.width,
      height: result.height,
      bearingX: 0,
      bearingY: 0,
      advance: result.width,
    });
  }

  /**
   * 生成纹理图集
   */
  generateAtlas(maxWidth = 512): {
    textureData: Uint8Array;
    glyphs: Map<string, MSDFGlyph & { atlasX: number; atlasY: number }>;
    atlasWidth: number;
    atlasHeight: number;
  } {
    const glyphsArray = Array.from(this.glyphs.values());
    const positionedGlyphs = new Map();

    // 简单布局算法
    let x = 0,
      y = 0,
      rowHeight = 0;
    const padding = 2;

    glyphsArray.forEach((glyph) => {
      if (x + glyph.width + padding > maxWidth) {
        x = 0;
        y += rowHeight + padding;
        rowHeight = 0;
      }

      const positionedGlyph = {
        ...glyph,
        atlasX: x,
        atlasY: y,
      };

      positionedGlyphs.set(glyph.char, positionedGlyph);

      x += glyph.width + padding;
      rowHeight = Math.max(rowHeight, glyph.height);
    });

    const atlasHeight = y + rowHeight;
    const textureData = new Uint8Array(maxWidth * atlasHeight * 3);

    // 将各个字形复制到图集中
    positionedGlyphs.forEach((glyph) => {
      this.copyGlyphToAtlas(glyph, textureData, maxWidth);
    });

    return {
      textureData,
      glyphs: positionedGlyphs,
      atlasWidth: maxWidth,
      atlasHeight,
    };
  }

  private copyGlyphToAtlas(
    glyph: MSDFGlyph & { atlasX: number; atlasY: number },
    atlasData: Uint8Array,
    atlasWidth: number,
  ): void {
    for (let y = 0; y < glyph.height; y++) {
      for (let x = 0; x < glyph.width; x++) {
        const srcIdx = (y * glyph.width + x) * 3;
        const dstIdx = ((glyph.atlasY + y) * atlasWidth + (glyph.atlasX + x)) * 3;

        atlasData[dstIdx] = glyph.data[srcIdx];
        atlasData[dstIdx + 1] = glyph.data[srcIdx + 1];
        atlasData[dstIdx + 2] = glyph.data[srcIdx + 2];
      }
    }
  }

  /**
   * 导出字体信息 JSON
   */
  exportFontInfo(): string {
    const fontInfo: any = {
      atlas: { width: 512, height: 512 },
      glyphs: {},
    };

    this.glyphs.forEach((glyph, char) => {
      fontInfo.glyphs[char] = {
        width: glyph.width,
        height: glyph.height,
        bearingX: glyph.bearingX,
        bearingY: glyph.bearingY,
        advance: glyph.advance,
      };
    });

    return JSON.stringify(fontInfo, null, 2);
  }
}
