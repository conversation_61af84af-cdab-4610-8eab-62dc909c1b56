<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSDF中文渲染简单示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        
        canvas {
            border: 1px solid #333;
            background: #000;
            display: block;
            margin: 20px auto;
        }
        
        .controls {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        
        .control-group {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        label {
            min-width: 120px;
            color: #ccc;
        }
        
        input[type="range"] {
            flex: 1;
            max-width: 200px;
        }
        
        input[type="text"] {
            flex: 1;
            max-width: 400px;
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            color: white;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .value {
            min-width: 80px;
            color: #fff;
            font-weight: bold;
        }
        
        button {
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .status {
            background: #444;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h2>MSDF中文字体渲染演示</h2>
        
        <div class="control-group">
            <label>文本内容:</label>
            <input type="text" id="textInput" value="你好世界！MSDF渲染" placeholder="输入要渲染的中文文本">
        </div>
        
        <div class="control-group">
            <label>字体大小:</label>
            <input type="range" id="fontSizeSlider" min="16" max="120" value="48">
            <span class="value" id="fontSizeValue">48px</span>
        </div>
        
        <div class="control-group">
            <label>描边宽度:</label>
            <input type="range" id="outlineSlider" min="0" max="0.3" step="0.01" value="0.05">
            <span class="value" id="outlineValue">0.05</span>
        </div>
        
        <div class="control-group">
            <label>平滑度:</label>
            <input type="range" id="smoothnessSlider" min="0.01" max="0.2" step="0.01" value="0.05">
            <span class="value" id="smoothnessValue">0.05</span>
        </div>
        
        <div class="control-group">
            <label>文本颜色:</label>
            <input type="color" id="colorPicker" value="#ffffff">
        </div>
        
        <div class="control-group">
            <label>描边颜色:</label>
            <input type="color" id="outlineColorPicker" value="#000000">
        </div>
        
        <div class="control-group">
            <button onclick="generateFont()">生成字体图集</button>
            <button onclick="resetView()">重置视图</button>
        </div>
        
        <div class="status" id="status">准备就绪</div>
    </div>
    
    <canvas id="canvas" width="800" height="400"></canvas>
    
    <div class="controls">
        <div class="info">
            <h3>MSDF渲染技术说明</h3>
            <p><strong>MSDF (Multi-channel Signed Distance Field)</strong> 是一种先进的文本渲染技术：</p>
            <ul>
                <li><strong>高质量缩放</strong>：支持任意大小缩放而不失真</li>
                <li><strong>中文友好</strong>：特别适合复杂的中文字符渲染</li>
                <li><strong>GPU加速</strong>：利用WebGL2进行高效渲染</li>
                <li><strong>丰富效果</strong>：支持描边、阴影、发光等效果</li>
                <li><strong>内存高效</strong>：相比位图字体占用更少内存</li>
            </ul>
            <p>本演示展示了如何在WebGL2中实现MSDF文本渲染，包括动态字体生成和实时参数调整。</p>
        </div>
    </div>

    <script>
        // 简化的MSDF渲染器
        class SimpleMSDFRenderer {
            constructor(canvas) {
                this.canvas = canvas;
                this.gl = canvas.getContext('webgl2');
                if (!this.gl) {
                    throw new Error('WebGL2 not supported');
                }
                
                this.program = null;
                this.vao = null;
                this.vertexBuffer = null;
                this.indexBuffer = null;
                this.fontTexture = null;
                this.fontMetrics = null;
                
                this.init();
                this.generateDefaultFont();
            }
            
            init() {
                const gl = this.gl;
                
                // 顶点着色器
                const vertexShaderSource = `#version 300 es
                    precision highp float;
                    
                    in vec2 a_position;
                    in vec2 a_texCoord;
                    
                    uniform vec2 u_resolution;
                    uniform vec2 u_translation;
                    uniform float u_scale;
                    
                    out vec2 v_texCoord;
                    
                    void main() {
                        vec2 position = (a_position * u_scale + u_translation) / u_resolution * 2.0 - 1.0;
                        gl_Position = vec4(position.x, -position.y, 0.0, 1.0);
                        v_texCoord = a_texCoord;
                    }
                `;
                
                // 片段着色器 - 高质量MSDF实现
                const fragmentShaderSource = `#version 300 es
                    precision highp float;
                    
                    in vec2 v_texCoord;
                    
                    uniform sampler2D u_msdfTexture;
                    uniform vec3 u_color;
                    uniform vec3 u_outlineColor;
                    uniform float u_pxRange;
                    uniform float u_outlineWidth;
                    uniform float u_smoothness;
                    
                    out vec4 fragColor;
                    
                    float median(float r, float g, float b) {
                        return max(min(r, g), min(max(r, g), b));
                    }
                    
                    float screenPxRange() {
                        vec2 unitRange = vec2(u_pxRange) / vec2(textureSize(u_msdfTexture, 0));
                        vec2 screenTexSize = vec2(1.0) / fwidth(v_texCoord);
                        return max(0.5 * dot(unitRange, screenTexSize), 1.0);
                    }
                    
                    void main() {
                        vec3 msd = texture(u_msdfTexture, v_texCoord).rgb;
                        float sd = median(msd.r, msd.g, msd.b);
                        float screenPxDistance = screenPxRange() * (sd - 0.5);
                        
                        // 主文本渲染
                        float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
                        
                        // 应用平滑度
                        alpha = smoothstep(0.5 - u_smoothness, 0.5 + u_smoothness, sd);
                        
                        vec3 finalColor = u_color;
                        float finalAlpha = alpha;
                        
                        // 描边效果
                        if (u_outlineWidth > 0.0) {
                            float outlineAlpha = smoothstep(0.5 - u_outlineWidth - u_smoothness, 
                                                          0.5 - u_outlineWidth + u_smoothness, sd);
                            finalColor = mix(u_outlineColor, u_color, alpha);
                            finalAlpha = max(outlineAlpha, alpha);
                        }
                        
                        fragColor = vec4(finalColor, finalAlpha);
                    }
                `;
                
                this.program = this.createProgram(vertexShaderSource, fragmentShaderSource);
                
                // 获取uniform位置
                this.uniforms = {
                    resolution: gl.getUniformLocation(this.program, 'u_resolution'),
                    translation: gl.getUniformLocation(this.program, 'u_translation'),
                    scale: gl.getUniformLocation(this.program, 'u_scale'),
                    msdfTexture: gl.getUniformLocation(this.program, 'u_msdfTexture'),
                    color: gl.getUniformLocation(this.program, 'u_color'),
                    outlineColor: gl.getUniformLocation(this.program, 'u_outlineColor'),
                    pxRange: gl.getUniformLocation(this.program, 'u_pxRange'),
                    outlineWidth: gl.getUniformLocation(this.program, 'u_outlineWidth'),
                    smoothness: gl.getUniformLocation(this.program, 'u_smoothness')
                };
                
                // 创建VAO
                this.vao = gl.createVertexArray();
                gl.bindVertexArray(this.vao);
                
                // 创建缓冲区
                this.vertexBuffer = gl.createBuffer();
                this.indexBuffer = gl.createBuffer();
                
                // 设置顶点属性
                const positionLocation = gl.getAttribLocation(this.program, 'a_position');
                const texCoordLocation = gl.getAttribLocation(this.program, 'a_texCoord');
                
                gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
                gl.enableVertexAttribArray(positionLocation);
                gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);
                gl.enableVertexAttribArray(texCoordLocation);
                gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);
                
                gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
                
                // 启用混合
                gl.enable(gl.BLEND);
                gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
            }
            
            createProgram(vertexSource, fragmentSource) {
                const gl = this.gl;
                
                const vertexShader = this.createShader(gl.VERTEX_SHADER, vertexSource);
                const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fragmentSource);
                
                const program = gl.createProgram();
                gl.attachShader(program, vertexShader);
                gl.attachShader(program, fragmentShader);
                gl.linkProgram(program);
                
                if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                    throw new Error('Program link error: ' + gl.getProgramInfoLog(program));
                }
                
                return program;
            }
            
            createShader(type, source) {
                const gl = this.gl;
                const shader = gl.createShader(type);
                gl.shaderSource(shader, source);
                gl.compileShader(shader);
                
                if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                    throw new Error('Shader compile error: ' + gl.getShaderInfoLog(shader));
                }
                
                return shader;
            }
            
            // 生成默认字体
            generateDefaultFont() {
                const size = 1024;
                const cellSize = 64;
                const cols = Math.floor(size / cellSize);
                
                // 常用中文字符
                const chars = ['你', '好', '世', '界', 'M', 'S', 'D', 'F', '渲', '染', '测', '试', '中', '文', '字', '体'];
                
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');
                
                // 生成字体图集
                this.fontMetrics = { chars: new Map() };
                
                chars.forEach((char, index) => {
                    const col = index % cols;
                    const row = Math.floor(index / cols);
                    const x = col * cellSize;
                    const y = row * cellSize;
                    
                    // 生成MSDF数据
                    this.generateCharMSDF(ctx, char, x, y, cellSize);
                    
                    // 保存字符度量
                    this.fontMetrics.chars.set(char, {
                        x: x / size,
                        y: y / size,
                        width: cellSize / size,
                        height: cellSize / size,
                        advance: cellSize * 0.8
                    });
                });
                
                // 创建纹理
                this.createTexture(canvas);
                updateStatus('字体图集生成完成');
            }
            
            generateCharMSDF(ctx, char, x, y, size) {
                // 清除区域
                ctx.clearRect(x, y, size, size);
                
                // 设置字体
                ctx.font = `${size * 0.8}px SimHei, Arial, sans-serif`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                // 生成简化的MSDF效果
                const centerX = x + size / 2;
                const centerY = y + size / 2;
                
                // 创建渐变来模拟MSDF
                const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, size / 2);
                gradient.addColorStop(0, 'rgb(255, 255, 255)');
                gradient.addColorStop(0.5, 'rgb(128, 128, 128)');
                gradient.addColorStop(1, 'rgb(0, 0, 0)');
                
                ctx.fillStyle = gradient;
                ctx.fillText(char, centerX, centerY);
                
                // 添加描边效果
                ctx.strokeStyle = 'rgb(64, 64, 64)';
                ctx.lineWidth = 2;
                ctx.strokeText(char, centerX, centerY);
            }
            
            createTexture(canvas) {
                const gl = this.gl;
                
                if (this.fontTexture) {
                    gl.deleteTexture(this.fontTexture);
                }
                
                this.fontTexture = gl.createTexture();
                gl.bindTexture(gl.TEXTURE_2D, this.fontTexture);
                gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGB, gl.RGB, gl.UNSIGNED_BYTE, canvas);
                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
            }
            
            render(text, x, y, scale, color, outlineColor, outlineWidth, smoothness) {
                const gl = this.gl;
                
                gl.viewport(0, 0, this.canvas.width, this.canvas.height);
                gl.clearColor(0.1, 0.1, 0.1, 1);
                gl.clear(gl.COLOR_BUFFER_BIT);
                
                if (!this.fontTexture || !this.fontMetrics) return;
                
                gl.useProgram(this.program);
                gl.bindVertexArray(this.vao);
                
                // 设置uniforms
                gl.uniform2f(this.uniforms.resolution, this.canvas.width, this.canvas.height);
                gl.uniform2f(this.uniforms.translation, x, y);
                gl.uniform1f(this.uniforms.scale, scale);
                gl.uniform3f(this.uniforms.color, color[0], color[1], color[2]);
                gl.uniform3f(this.uniforms.outlineColor, outlineColor[0], outlineColor[1], outlineColor[2]);
                gl.uniform1f(this.uniforms.pxRange, 4.0);
                gl.uniform1f(this.uniforms.outlineWidth, outlineWidth);
                gl.uniform1f(this.uniforms.smoothness, smoothness);
                
                // 绑定纹理
                gl.activeTexture(gl.TEXTURE0);
                gl.bindTexture(gl.TEXTURE_2D, this.fontTexture);
                gl.uniform1i(this.uniforms.msdfTexture, 0);
                
                // 渲染每个字符
                let offsetX = 0;
                for (let i = 0; i < text.length; i++) {
                    const char = text[i];
                    const charMetrics = this.fontMetrics.chars.get(char);
                    
                    if (!charMetrics) {
                        offsetX += scale * 32; // 空格
                        continue;
                    }
                    
                    const charWidth = charMetrics.advance * scale;
                    const charHeight = charMetrics.advance * scale;
                    
                    // 顶点数据 (position + texCoord)
                    const vertices = new Float32Array([
                        offsetX, 0, charMetrics.x, charMetrics.y,
                        offsetX + charWidth, 0, charMetrics.x + charMetrics.width, charMetrics.y,
                        offsetX + charWidth, charHeight, charMetrics.x + charMetrics.width, charMetrics.y + charMetrics.height,
                        offsetX, charHeight, charMetrics.x, charMetrics.y + charMetrics.height
                    ]);
                    
                    const indices = new Uint16Array([0, 1, 2, 0, 2, 3]);
                    
                    gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
                    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.DYNAMIC_DRAW);
                    
                    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
                    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.DYNAMIC_DRAW);
                    
                    gl.drawElements(gl.TRIANGLES, 6, gl.UNSIGNED_SHORT, 0);
                    
                    offsetX += charWidth * 0.9; // 字符间距
                }
            }
        }
        
        // 状态更新函数
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        // 初始化
        const canvas = document.getElementById('canvas');
        let renderer;
        
        try {
            renderer = new SimpleMSDFRenderer(canvas);
            updateStatus('MSDF渲染器初始化成功');
        } catch (error) {
            updateStatus('错误: ' + error.message);
            console.error(error);
        }
        
        // 控制元素
        const textInput = document.getElementById('textInput');
        const fontSizeSlider = document.getElementById('fontSizeSlider');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const outlineSlider = document.getElementById('outlineSlider');
        const outlineValue = document.getElementById('outlineValue');
        const smoothnessSlider = document.getElementById('smoothnessSlider');
        const smoothnessValue = document.getElementById('smoothnessValue');
        const colorPicker = document.getElementById('colorPicker');
        const outlineColorPicker = document.getElementById('outlineColorPicker');
        
        // 渲染函数
        function render() {
            if (!renderer) return;
            
            const text = textInput.value;
            const fontSize = parseInt(fontSizeSlider.value);
            const outline = parseFloat(outlineSlider.value);
            const smoothness = parseFloat(smoothnessSlider.value);
            const colorHex = colorPicker.value;
            const outlineColorHex = outlineColorPicker.value;
            
            // 转换颜色
            const color = [
                parseInt(colorHex.substr(1, 2), 16) / 255,
                parseInt(colorHex.substr(3, 2), 16) / 255,
                parseInt(colorHex.substr(5, 2), 16) / 255
            ];
            
            const outlineColor = [
                parseInt(outlineColorHex.substr(1, 2), 16) / 255,
                parseInt(outlineColorHex.substr(3, 2), 16) / 255,
                parseInt(outlineColorHex.substr(5, 2), 16) / 255
            ];
            
            const scale = fontSize / 64;
            const x = 50;
            const y = canvas.height / 2 - fontSize / 2;
            
            renderer.render(text, x, y, scale, color, outlineColor, outline, smoothness);
        }
        
        // 事件监听
        textInput.addEventListener('input', render);
        fontSizeSlider.addEventListener('input', () => {
            fontSizeValue.textContent = fontSizeSlider.value + 'px';
            render();
        });
        outlineSlider.addEventListener('input', () => {
            outlineValue.textContent = outlineSlider.value;
            render();
        });
        smoothnessSlider.addEventListener('input', () => {
            smoothnessValue.textContent = smoothnessSlider.value;
            render();
        });
        colorPicker.addEventListener('input', render);
        outlineColorPicker.addEventListener('input', render);
        
        // 生成字体函数
        function generateFont() {
            updateStatus('正在重新生成字体图集...');
            setTimeout(() => {
                renderer.generateDefaultFont();
                render();
            }, 100);
        }
        
        // 重置视图函数
        function resetView() {
            textInput.value = '你好世界！MSDF渲染';
            fontSizeSlider.value = 48;
            outlineSlider.value = 0.05;
            smoothnessSlider.value = 0.05;
            colorPicker.value = '#ffffff';
            outlineColorPicker.value = '#000000';
            
            fontSizeValue.textContent = '48px';
            outlineValue.textContent = '0.05';
            smoothnessValue.textContent = '0.05';
            
            render();
            updateStatus('视图已重置');
        }
        
        // 全局函数
        window.generateFont = generateFont;
        window.resetView = resetView;
        
        // 初始渲染
        setTimeout(render, 100);
        
        // 处理canvas大小变化
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * devicePixelRatio;
            canvas.height = rect.height * devicePixelRatio;
            render();
        }
        
        window.addEventListener('resize', resizeCanvas);
    </script>
</body>
</html>
