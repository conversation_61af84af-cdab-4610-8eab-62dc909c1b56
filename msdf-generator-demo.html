<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas 2D MSDF纹理生成器</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
        }
        
        .controls {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            margin: 15px 0;
            gap: 15px;
        }
        
        .control-group label {
            min-width: 120px;
            color: #ccc;
        }
        
        .control-group input[type="text"] {
            flex: 1;
            max-width: 300px;
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            color: white;
            border-radius: 4px;
        }
        
        .control-group input[type="range"] {
            flex: 1;
            max-width: 200px;
        }
        
        .control-group select {
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            color: white;
            border-radius: 4px;
        }
        
        .value {
            min-width: 80px;
            color: #fff;
            font-weight: bold;
            font-family: monospace;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .preview-section {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .preview-item {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .preview-item h3 {
            margin: 0 0 15px 0;
            color: #ccc;
        }
        
        canvas {
            border: 1px solid #333;
            background: #000;
            border-radius: 4px;
            max-width: 100%;
        }
        
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .status {
            background: #444;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .download-links {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .download-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .download-links a:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas 2D MSDF纹理生成器</h1>
        
        <div class="controls">
            <h3>生成参数</h3>
            
            <div class="control-group">
                <label>字符列表:</label>
                <input type="text" id="charactersInput" value="你好世界MSDF" placeholder="输入要生成的字符">
            </div>
            
            <div class="control-group">
                <label>字体大小:</label>
                <input type="range" id="fontSizeSlider" min="32" max="128" value="64">
                <span class="value" id="fontSizeValue">64px</span>
            </div>
            
            <div class="control-group">
                <label>内边距:</label>
                <input type="range" id="paddingSlider" min="4" max="16" value="8">
                <span class="value" id="paddingValue">8px</span>
            </div>
            
            <div class="control-group">
                <label>距离范围:</label>
                <input type="range" id="distanceRangeSlider" min="2" max="8" value="4">
                <span class="value" id="distanceRangeValue">4</span>
            </div>
            
            <div class="control-group">
                <label>图集大小:</label>
                <select id="atlasSizeSelect">
                    <option value="256">256x256</option>
                    <option value="512" selected>512x512</option>
                    <option value="1024">1024x1024</option>
                    <option value="2048">2048x2048</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>字体族:</label>
                <select id="fontFamilySelect">
                    <option value="SimHei, Arial, sans-serif">SimHei (黑体)</option>
                    <option value="Microsoft YaHei, Arial, sans-serif">Microsoft YaHei (微软雅黑)</option>
                    <option value="Arial, sans-serif">Arial</option>
                    <option value="Times New Roman, serif">Times New Roman</option>
                </select>
            </div>
            
            <div class="button-group">
                <button onclick="generateMSDF()">生成MSDF图集</button>
                <button onclick="generatePreset('chinese')">中文预设</button>
                <button onclick="generatePreset('english')">英文预设</button>
                <button onclick="generatePreset('numbers')">数字预设</button>
                <button onclick="clearPreviews()">清除预览</button>
            </div>
        </div>
        
        <div class="status" id="status">准备就绪</div>
        
        <div class="preview-section">
            <div class="preview-item">
                <h3>原始字符</h3>
                <canvas id="originalCanvas" width="400" height="300"></canvas>
            </div>
            <div class="preview-item">
                <h3>MSDF图集</h3>
                <canvas id="msdfCanvas" width="400" height="300"></canvas>
            </div>
        </div>
        
        <div class="download-links" id="downloadSection" style="display: none;">
            <h3>下载生成的文件</h3>
            <a href="#" id="downloadAtlas" download="msdf-atlas.png">下载图集纹理</a>
            <a href="#" id="downloadJSON" download="msdf-data.json">下载字体数据</a>
        </div>
        
        <div class="info">
            <h3>生成信息</h3>
            <div id="generationInfo">等待生成...</div>
        </div>
        
        <div class="info">
            <h3>技术说明</h3>
            <ul>
                <li><strong>MSDF (Multi-channel Signed Distance Field)</strong>: 高质量可缩放字体渲染技术</li>
                <li><strong>Canvas 2D实现</strong>: 使用纯JavaScript和Canvas 2D API生成距离场</li>
                <li><strong>多通道编码</strong>: RGB三个通道存储不同方向的距离信息</li>
                <li><strong>自动布局</strong>: 智能排列字符到图集纹理中</li>
                <li><strong>兼容格式</strong>: 生成标准的BMFont格式JSON数据</li>
            </ul>
        </div>
    </div>

    <script type="module">
        import { 
            generateMSDFAtlas, 
            generateChineseMSDFFont, 
            exportMSDFData 
        } from './msdf-generator-canvas2d.js';
        
        // 获取控制元素
        const charactersInput = document.getElementById('charactersInput');
        const fontSizeSlider = document.getElementById('fontSizeSlider');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const paddingSlider = document.getElementById('paddingSlider');
        const paddingValue = document.getElementById('paddingValue');
        const distanceRangeSlider = document.getElementById('distanceRangeSlider');
        const distanceRangeValue = document.getElementById('distanceRangeValue');
        const atlasSizeSelect = document.getElementById('atlasSizeSelect');
        const fontFamilySelect = document.getElementById('fontFamilySelect');
        
        // 预览画布
        const originalCanvas = document.getElementById('originalCanvas');
        const msdfCanvas = document.getElementById('msdfCanvas');
        const originalCtx = originalCanvas.getContext('2d');
        const msdfCtx = msdfCanvas.getContext('2d');
        
        let currentAtlas = null;
        
        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log(message);
        }
        
        // 更新滑块值显示
        function updateSliderValues() {
            fontSizeValue.textContent = fontSizeSlider.value + 'px';
            paddingValue.textContent = paddingSlider.value + 'px';
            distanceRangeValue.textContent = distanceRangeSlider.value;
        }
        
        // 清除预览
        function clearPreviews() {
            originalCtx.clearRect(0, 0, originalCanvas.width, originalCanvas.height);
            msdfCtx.clearRect(0, 0, msdfCanvas.width, msdfCanvas.height);
            document.getElementById('downloadSection').style.display = 'none';
            document.getElementById('generationInfo').textContent = '等待生成...';
        }
        
        // 生成MSDF
        function generateMSDF() {
            try {
                updateStatus('正在生成MSDF图集...');
                
                const characters = Array.from(new Set(charactersInput.value.split(''))).filter(c => c.trim());
                const fontSize = parseInt(fontSizeSlider.value);
                const padding = parseInt(paddingSlider.value);
                const distanceRange = parseInt(distanceRangeSlider.value);
                const atlasSize = parseInt(atlasSizeSelect.value);
                const fontFamily = fontFamilySelect.value;
                
                if (characters.length === 0) {
                    updateStatus('错误: 请输入要生成的字符');
                    return;
                }
                
                const config = {
                    fontSize: fontSize,
                    padding: padding,
                    distanceRange: distanceRange,
                    atlasWidth: atlasSize,
                    atlasHeight: atlasSize,
                    fontFamily: fontFamily
                };
                
                // 生成图集
                currentAtlas = generateMSDFAtlas(characters, config);
                
                // 显示原始字符预览
                showOriginalPreview(characters, fontSize, fontFamily);
                
                // 显示MSDF图集
                showMSDFPreview(currentAtlas.canvas);
                
                // 更新信息
                updateGenerationInfo(currentAtlas);
                
                // 创建下载链接
                createDownloadLinks(currentAtlas);
                
                updateStatus(`MSDF图集生成完成! 包含${characters.length}个字符`);
                
            } catch (error) {
                updateStatus('生成失败: ' + error.message);
                console.error(error);
            }
        }
        
        // 显示原始字符预览
        function showOriginalPreview(characters, fontSize, fontFamily) {
            originalCtx.clearRect(0, 0, originalCanvas.width, originalCanvas.height);
            originalCtx.fillStyle = '#333';
            originalCtx.fillRect(0, 0, originalCanvas.width, originalCanvas.height);
            
            originalCtx.font = `${fontSize}px ${fontFamily}`;
            originalCtx.fillStyle = 'white';
            originalCtx.textAlign = 'left';
            originalCtx.textBaseline = 'top';
            
            let x = 10;
            let y = 10;
            const lineHeight = fontSize + 10;
            
            for (const char of characters) {
                const metrics = originalCtx.measureText(char);
                
                if (x + metrics.width > originalCanvas.width - 10) {
                    x = 10;
                    y += lineHeight;
                }
                
                if (y + fontSize > originalCanvas.height - 10) break;
                
                originalCtx.fillText(char, x, y);
                x += metrics.width + 10;
            }
        }
        
        // 显示MSDF图集预览
        function showMSDFPreview(atlasCanvas) {
            msdfCtx.clearRect(0, 0, msdfCanvas.width, msdfCanvas.height);
            
            // 缩放图集以适应预览画布
            const scale = Math.min(
                msdfCanvas.width / atlasCanvas.width,
                msdfCanvas.height / atlasCanvas.height
            );
            
            const scaledWidth = atlasCanvas.width * scale;
            const scaledHeight = atlasCanvas.height * scale;
            const offsetX = (msdfCanvas.width - scaledWidth) / 2;
            const offsetY = (msdfCanvas.height - scaledHeight) / 2;
            
            msdfCtx.drawImage(atlasCanvas, offsetX, offsetY, scaledWidth, scaledHeight);
        }
        
        // 更新生成信息
        function updateGenerationInfo(atlas) {
            const info = `
字符数量: ${atlas.characters.size}
图集尺寸: ${atlas.config.atlasWidth}x${atlas.config.atlasHeight}
字体大小: ${atlas.config.fontSize}px
字体族: ${atlas.config.fontFamily}
内边距: ${atlas.config.padding}px
距离范围: ${atlas.config.distanceRange}
行高: ${atlas.metrics.lineHeight.toFixed(1)}
基线: ${atlas.metrics.baseline.toFixed(1)}
            `.trim();
            
            document.getElementById('generationInfo').textContent = info;
        }
        
        // 创建下载链接
        function createDownloadLinks(atlas) {
            // 图集纹理
            atlas.canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                document.getElementById('downloadAtlas').href = url;
            }, 'image/png');
            
            // JSON数据
            const jsonData = exportMSDFData(atlas);
            const jsonBlob = new Blob([jsonData], { type: 'application/json' });
            const jsonUrl = URL.createObjectURL(jsonBlob);
            document.getElementById('downloadJSON').href = jsonUrl;
            
            document.getElementById('downloadSection').style.display = 'block';
        }
        
        // 预设生成
        function generatePreset(type) {
            switch (type) {
                case 'chinese':
                    charactersInput.value = '你好世界中文字体测试一二三四五六七八九十';
                    fontFamilySelect.value = 'SimHei, Arial, sans-serif';
                    break;
                case 'english':
                    charactersInput.value = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
                    fontFamilySelect.value = 'Arial, sans-serif';
                    break;
                case 'numbers':
                    charactersInput.value = '0123456789+-*/=.,%$()[]{}';
                    fontFamilySelect.value = 'Arial, sans-serif';
                    break;
            }
            generateMSDF();
        }
        
        // 事件监听
        fontSizeSlider.addEventListener('input', updateSliderValues);
        paddingSlider.addEventListener('input', updateSliderValues);
        distanceRangeSlider.addEventListener('input', updateSliderValues);
        
        // 全局函数
        window.generateMSDF = generateMSDF;
        window.generatePreset = generatePreset;
        window.clearPreviews = clearPreviews;
        
        // 初始化
        updateSliderValues();
        updateStatus('MSDF生成器已就绪');
    </script>
</body>
</html>
