// generateMSDFAtlas数据用于WebGL2渲染的完整示例

import { generateMSDFAtlas } from './msdf-generator-canvas2d.js';
import type { MSDFAtlas } from './msdf-generator-canvas2d.js';
import { createMSDFRendererFromAtlas } from './webgl2-msdf-renderer.js';

// 步骤1: 使用generateMSDFAtlas生成数据
const createMSDFData = (characters: string[], fontSize: number = 64) => {
  console.log('🔧 步骤1: 生成MSDF图集数据');
  
  const config = {
    fontSize: fontSize,
    padding: 8,
    distanceRange: 4,
    atlasWidth: 512,
    atlasHeight: 512,
    fontFamily: 'SimHei, Arial, sans-serif'
  };
  
  // 调用generateMSDFAtlas生成数据
  const atlas = generateMSDFAtlas(characters, config);
  
  console.log('✅ MSDF图集生成完成:', {
    characters: atlas.characters.size,
    atlasSize: `${atlas.config.atlasWidth}x${atlas.config.atlasHeight}`,
    fontSize: atlas.config.fontSize,
    distanceRange: atlas.config.distanceRange
  });
  
  return atlas;
};

// 步骤2: 将MSDFAtlas数据转换为WebGL2可用的资源
const setupWebGL2Renderer = (canvas: HTMLCanvasElement, atlas: MSDFAtlas) => {
  console.log('🎮 步骤2: 创建WebGL2渲染器');
  
  // 使用atlas数据创建WebGL2渲染器
  const renderer = createMSDFRendererFromAtlas(canvas, atlas);
  
  console.log('✅ WebGL2渲染器创建完成:', {
    supportedChars: renderer.getSupportedCharacters(),
    atlasTexture: '已上传到GPU',
    shaders: '已编译并链接'
  });
  
  return renderer;
};

// 步骤3: 使用atlas数据进行文本渲染
const renderTextWithAtlasData = (
  renderer: any, 
  text: string, 
  options: {
    x?: number;
    y?: number;
    fontSize?: number;
    color?: [number, number, number];
    outlineColor?: [number, number, number];
    outlineWidth?: number;
  } = {}
) => {
  console.log('🎨 步骤3: 渲染文本');
  
  const renderOptions = {
    x: options.x || 50,
    y: options.y || 100,
    fontSize: options.fontSize || 48,
    color: options.color || [1, 1, 1],
    outlineColor: options.outlineColor || [0, 0, 0],
    outlineWidth: options.outlineWidth || 0.05,
    smoothness: 0.05
  };
  
  // 使用atlas数据渲染文本
  renderer.render(text, renderOptions);
  
  console.log('✅ 文本渲染完成:', {
    text: text,
    position: `(${renderOptions.x}, ${renderOptions.y})`,
    fontSize: renderOptions.fontSize,
    color: renderOptions.color
  });
};

// 完整的使用流程示例
const completeExample = async (canvasId: string) => {
  console.log('🚀 开始完整的MSDF渲染流程');
  
  try {
    // 获取canvas元素
    const canvas = document.getElementById(canvasId) as HTMLCanvasElement;
    if (!canvas) {
      throw new Error(`Canvas元素 '${canvasId}' 未找到`);
    }
    
    // 步骤1: 生成MSDF图集数据
    const characters = ['你', '好', '世', '界', 'H', 'e', 'l', 'o', 'W', 'r', 'd'];
    const atlas = createMSDFData(characters, 64);
    
    // 步骤2: 创建WebGL2渲染器
    const renderer = setupWebGL2Renderer(canvas, atlas);
    
    // 步骤3: 渲染不同的文本
    renderTextWithAtlasData(renderer, '你好世界', {
      x: 50,
      y: 100,
      fontSize: 48,
      color: [1, 0.8, 0.2]
    });
    
    // 渲染英文文本
    renderTextWithAtlasData(renderer, 'Hello World', {
      x: 50,
      y: 180,
      fontSize: 36,
      color: [0.2, 0.8, 1],
      outlineWidth: 0.1
    });
    
    console.log('🎉 完整流程执行成功!');
    
    return {
      atlas,
      renderer,
      // 提供便捷的渲染函数
      render: (text: string, options?: any) => {
        renderTextWithAtlasData(renderer, text, options);
      }
    };
    
  } catch (error) {
    console.error('❌ 流程执行失败:', error);
    throw error;
  }
};

// 数据结构说明和使用方法
const explainAtlasData = (atlas: MSDFAtlas) => {
  console.log('📊 MSDFAtlas数据结构说明:');
  
  // 1. 图集画布 - 包含所有字符的MSDF纹理
  console.log('1. atlas.canvas:', {
    type: 'HTMLCanvasElement',
    size: `${atlas.canvas.width}x${atlas.canvas.height}`,
    usage: '用于创建WebGL纹理'
  });
  
  // 2. 字符映射 - 每个字符在图集中的位置和度量信息
  console.log('2. atlas.characters:', {
    type: 'Map<string, CharacterInfo>',
    size: atlas.characters.size,
    example: Array.from(atlas.characters.entries())[0],
    usage: '用于生成文本几何数据'
  });
  
  // 3. 配置信息 - 生成参数
  console.log('3. atlas.config:', {
    fontSize: atlas.config.fontSize,
    distanceRange: atlas.config.distanceRange,
    atlasSize: `${atlas.config.atlasWidth}x${atlas.config.atlasHeight}`,
    usage: '用于WebGL渲染参数设置'
  });
  
  // 4. 字体度量 - 排版信息
  console.log('4. atlas.metrics:', {
    lineHeight: atlas.metrics.lineHeight,
    baseline: atlas.metrics.baseline,
    usage: '用于文本布局和对齐'
  });
};

// WebGL2渲染的关键步骤
const explainWebGL2Usage = () => {
  console.log('🎮 WebGL2渲染关键步骤:');
  
  console.log(`
1. 纹理创建:
   atlas.canvas → gl.texImage2D() → WebGL纹理

2. 几何生成:
   atlas.characters → 顶点坐标 + 纹理坐标 → 缓冲区

3. 着色器渲染:
   MSDF着色器 + 纹理采样 → 高质量文本

4. 参数传递:
   atlas.config.distanceRange → u_pxRange uniform
   字符位置信息 → 纹理坐标计算
  `);
};

// 性能优化建议
const performanceOptimizations = () => {
  console.log('⚡ 性能优化建议:');
  
  console.log(`
1. 图集复用:
   - 一次生成，多次使用
   - 缓存常用字符集

2. 批量渲染:
   - 合并相同字体的文本
   - 减少draw call次数

3. 动态加载:
   - 按需生成字符
   - 扩展现有图集

4. 内存管理:
   - 及时释放不用的图集
   - 使用适当的图集尺寸
  `);
};

// 导出示例函数
export {
  createMSDFData,
  setupWebGL2Renderer,
  renderTextWithAtlasData,
  completeExample,
  explainAtlasData,
  explainWebGL2Usage,
  performanceOptimizations
};

// 浏览器环境下的全局函数
if (typeof window !== 'undefined') {
  // 将示例函数挂载到全局对象
  (window as any).MSDFExample = {
    createMSDFData,
    setupWebGL2Renderer,
    renderTextWithAtlasData,
    completeExample,
    explainAtlasData,
    explainWebGL2Usage,
    performanceOptimizations
  };
  
  console.log('🌐 MSDF示例函数已挂载到 window.MSDFExample');
}
