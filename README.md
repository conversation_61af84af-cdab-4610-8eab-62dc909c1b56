不使用任何第三方测试类库, 直接使用console.assert进行单元测试

不使用第三方类库, 自定义组件函数系统如何在自定义的Graph组件函数中增加id map支持, 以及如何提供让子组件能调用父组件api的机制

```ts
type CompInstance = {type:CompFn, props:any};
type CompFn = (props:any) => UI | CompInstance | null | undefined;

function Graph({data}) {
  const keyMap = new Map<string, UI>();
  //如何增加id map支持, 将画布内的组件以及组件下面的子组件(可以自己控制是否加入)生成的ui对象加入到keyMap中
  const canvasUI = new CanvasUI();
  data.forEach((item) => {
  const ui = instanceUI(item);
  ui && canvasUI.add(ui);
  })
  return canvasUI;
}

function instanceUI(item: CompInstance) {
  let r = comp.type(comp.props);
  while (r && isComponentInstance(r)) {
    r = comp.type(comp.props);
  }
  return r;
}

function render(comp: CompInstance) {
  const ui = instanceUI(comp);
  ui && renderUI(ui);
}
```