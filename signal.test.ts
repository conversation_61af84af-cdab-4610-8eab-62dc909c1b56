import { signal, computed, readonly, batch, effect, when, asyncSignal } from './signal'

// 基础Signal测试
console.log('=== 基础Signal测试 ===')

const count = signal(0)
console.log('初始值:', count.get()) // 0

// 订阅变化
const unsubscribe = count.subscribe(value => {
  console.log('count变化:', value)
})

count.set(5) // 输出: count变化: 5
count.update(prev => prev + 1) // 输出: count变化: 6

console.log('当前值:', count.get()) // 6

// 计算Signal测试
console.log('\n=== 计算Signal测试 ===')

const doubled = computed(() => count.get() * 2)
const tripled = computed(() => count.get() * 3)

console.log('doubled:', doubled.get()) // 12
console.log('tripled:', tripled.get()) // 18

// 订阅计算Signal
const unsubscribeDoubled = doubled.subscribe(value => {
  console.log('doubled变化:', value)
})

count.set(10) // 输出: count变化: 10, doubled变化: 20

// 复杂计算依赖
console.log('\n=== 复杂依赖测试 ===')

const sum = computed(() => doubled.get() + tripled.get())
console.log('sum:', sum.get()) // 50 (20 + 30)

const unsubscribeSum = sum.subscribe(value => {
  console.log('sum变化:', value)
})

count.set(5) // 输出: count变化: 5, doubled变化: 10, sum变化: 25

// 只读Signal测试
console.log('\n=== 只读Signal测试 ===')

const readonlyCount = readonly(count)
console.log('只读count:', readonlyCount.get()) // 5

const unsubscribeReadonly = readonlyCount.subscribe(value => {
  console.log('只读count变化:', value)
})

count.set(8) // 输出: count变化: 8, doubled变化: 16, sum变化: 40, 只读count变化: 8

// 批量更新测试
console.log('\n=== 批量更新测试 ===')

const x = signal(1)
const y = signal(2)
const product = computed(() => x.get() * y.get())

product.subscribe(value => {
  console.log('product变化:', value)
})

console.log('批量更新前:', product.get()) // 2

batch(() => {
  x.set(3)
  y.set(4)
  // 只会触发一次product变化
}) // 输出: product变化: 12

// 副作用测试
console.log('\n=== 副作用测试 ===')

const name = signal('Alice')
const age = signal(25)

const cleanupEffect = effect(() => {
  console.log(`用户信息: ${name.get()}, 年龄: ${age.get()}`)
})

name.set('Bob') // 输出: 用户信息: Bob, 年龄: 25
age.set(30) // 输出: 用户信息: Bob, 年龄: 30

// 条件Signal测试
console.log('\n=== 条件Signal测试 ===')

const isAdult = signal(false)
const status = when(
  () => isAdult.get(),
  () => '成年人',
  () => '未成年'
)

console.log('状态:', status.get()) // 未成年

status.subscribe(value => {
  console.log('状态变化:', value)
})

isAdult.set(true) // 输出: 状态变化: 成年人

// 异步Signal测试
console.log('\n=== 异步Signal测试 ===')

const asyncData = asyncSignal(
  () => new Promise(resolve => {
    setTimeout(() => resolve('异步数据加载完成'), 1000)
  }),
  '加载中...'
)

console.log('异步数据初始值:', asyncData.get()) // 加载中...

asyncData.subscribe(value => {
  console.log('异步数据更新:', value)
})

// 1秒后输出: 异步数据更新: 异步数据加载完成

// 链式计算测试
console.log('\n=== 链式计算测试 ===')

const base = signal(2)
const squared = computed(() => base.get() ** 2)
const cubed = computed(() => squared.get() * base.get())
const final = computed(() => cubed.get() + squared.get())

console.log('base:', base.get()) // 2
console.log('squared:', squared.get()) // 4
console.log('cubed:', cubed.get()) // 8
console.log('final:', final.get()) // 12

final.subscribe(value => {
  console.log('final变化:', value)
})

base.set(3) // 输出: final变化: 36 (27 + 9)

// 性能测试
console.log('\n=== 性能测试 ===')

const perfSignal = signal(0)
const perfComputed = computed(() => perfSignal.get() * 2)

console.time('1000次更新')
for (let i = 0; i < 1000; i++) {
  perfSignal.set(i)
}
console.timeEnd('1000次更新')

// 清理订阅
console.log('\n=== 清理测试 ===')

unsubscribe()
unsubscribeDoubled()
unsubscribeSum()
unsubscribeReadonly()
cleanupEffect()

console.log('所有订阅已清理')

// 内存泄漏测试
console.log('\n=== 内存泄漏测试 ===')

function createSignalChain() {
  const s1 = signal(1)
  const s2 = computed(() => s1.get() * 2)
  const s3 = computed(() => s2.get() + 1)
  return { s1, s2, s3 }
}

// 创建多个信号链
for (let i = 0; i < 100; i++) {
  createSignalChain()
}

console.log('内存泄漏测试完成')

// 错误处理测试
console.log('\n=== 错误处理测试 ===')

const errorSignal = signal(1)
const errorComputed = computed(() => {
  const value = errorSignal.get()
  if (value === 0) {
    throw new Error('除零错误')
  }
  return 10 / value
})

try {
  console.log('正常计算:', errorComputed.get()) // 10
  errorSignal.set(0)
  console.log('错误计算:', errorComputed.get()) // 抛出错误
} catch (error) {
  console.log('捕获错误:', error.message)
}

console.log('\n=== 所有测试完成 ===')
