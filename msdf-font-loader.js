// MSDF字体加载器和渲染器
export class MSDFFontLoader {
    constructor() {
        this.fonts = new Map();
        this.atlasCache = new Map();
    }

    // 加载MSDF字体数据
    async loadFont(fontName, atlasUrl, metricsUrl) {
        try {
            // 加载字体图集纹理
            const atlasImage = await this.loadImage(atlasUrl);
            
            // 加载字体度量数据
            const metricsResponse = await fetch(metricsUrl);
            const metrics = await metricsResponse.json();
            
            const fontData = {
                atlas: atlasImage,
                metrics: metrics,
                glyphs: new Map()
            };
            
            // 构建字形映射
            if (metrics.glyphs) {
                metrics.glyphs.forEach(glyph => {
                    fontData.glyphs.set(glyph.unicode, glyph);
                });
            }
            
            this.fonts.set(fontName, fontData);
            return fontData;
        } catch (error) {
            console.error(`Failed to load font ${fontName}:`, error);
            throw error;
        }
    }

    // 加载图片
    loadImage(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = url;
        });
    }

    // 获取字体数据
    getFont(fontName) {
        return this.fonts.get(fontName);
    }

    // 生成文本的几何数据
    generateTextGeometry(fontName, text, fontSize = 48) {
        const font = this.getFont(fontName);
        if (!font) {
            throw new Error(`Font ${fontName} not loaded`);
        }

        const vertices = [];
        const indices = [];
        const scale = fontSize / font.metrics.size;
        
        let x = 0;
        let vertexIndex = 0;

        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            const charCode = char.charCodeAt(0);
            const glyph = font.glyphs.get(charCode);
            
            if (!glyph) {
                // 如果字符不存在，使用空格或默认字符
                x += fontSize * 0.5;
                continue;
            }

            // 计算字符在屏幕上的位置和大小
            const glyphX = x + glyph.planeBounds.left * scale;
            const glyphY = glyph.planeBounds.bottom * scale;
            const glyphWidth = (glyph.planeBounds.right - glyph.planeBounds.left) * scale;
            const glyphHeight = (glyph.planeBounds.top - glyph.planeBounds.bottom) * scale;

            // 计算纹理坐标
            const atlasWidth = font.metrics.atlas.width;
            const atlasHeight = font.metrics.atlas.height;
            const u1 = glyph.atlasBounds.left / atlasWidth;
            const v1 = glyph.atlasBounds.bottom / atlasHeight;
            const u2 = glyph.atlasBounds.right / atlasWidth;
            const v2 = glyph.atlasBounds.top / atlasHeight;

            // 添加四个顶点 (x, y, u, v)
            vertices.push(
                glyphX, glyphY, u1, v1,                    // 左下
                glyphX + glyphWidth, glyphY, u2, v1,       // 右下
                glyphX + glyphWidth, glyphY + glyphHeight, u2, v2, // 右上
                glyphX, glyphY + glyphHeight, u1, v2       // 左上
            );

            // 添加两个三角形的索引
            indices.push(
                vertexIndex, vertexIndex + 1, vertexIndex + 2,
                vertexIndex, vertexIndex + 2, vertexIndex + 3
            );

            vertexIndex += 4;
            x += glyph.advance * scale;
        }

        return {
            vertices: new Float32Array(vertices),
            indices: new Uint16Array(indices),
            vertexCount: vertices.length / 4,
            indexCount: indices.length
        };
    }
}

// MSDF渲染器
export class MSDFTextRenderer {
    constructor(gl) {
        this.gl = gl;
        this.program = null;
        this.vao = null;
        this.vertexBuffer = null;
        this.indexBuffer = null;
        this.textures = new Map();
        
        this.init();
    }

    init() {
        const gl = this.gl;

        // 顶点着色器
        const vertexShaderSource = `#version 300 es
            precision highp float;
            
            in vec2 a_position;
            in vec2 a_texCoord;
            
            uniform mat4 u_projection;
            uniform mat4 u_modelView;
            
            out vec2 v_texCoord;
            
            void main() {
                gl_Position = u_projection * u_modelView * vec4(a_position, 0.0, 1.0);
                v_texCoord = a_texCoord;
            }
        `;

        // 片段着色器 - 高质量MSDF实现
        const fragmentShaderSource = `#version 300 es
            precision highp float;
            
            in vec2 v_texCoord;
            
            uniform sampler2D u_msdfTexture;
            uniform vec4 u_color;
            uniform float u_pxRange;
            uniform float u_outlineWidth;
            uniform vec4 u_outlineColor;
            uniform float u_shadowOffset;
            uniform vec4 u_shadowColor;
            uniform float u_smoothness;
            
            out vec4 fragColor;
            
            float median(float r, float g, float b) {
                return max(min(r, g), min(max(r, g), b));
            }
            
            float screenPxRange() {
                vec2 unitRange = vec2(u_pxRange) / vec2(textureSize(u_msdfTexture, 0));
                vec2 screenTexSize = vec2(1.0) / fwidth(v_texCoord);
                return max(0.5 * dot(unitRange, screenTexSize), 1.0);
            }
            
            void main() {
                vec3 msd = texture(u_msdfTexture, v_texCoord).rgb;
                float sd = median(msd.r, msd.g, msd.b);
                float screenPxDistance = screenPxRange() * (sd - 0.5);
                
                // 主文本
                float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
                alpha = smoothstep(0.5 - u_smoothness, 0.5 + u_smoothness, sd);
                
                vec4 finalColor = u_color;
                finalColor.a *= alpha;
                
                // 描边效果
                if (u_outlineWidth > 0.0) {
                    float outlineAlpha = smoothstep(0.5 - u_outlineWidth, 0.5, sd);
                    vec4 outlineResult = mix(u_outlineColor, finalColor, alpha);
                    outlineResult.a = max(outlineAlpha, alpha);
                    finalColor = outlineResult;
                }
                
                // 阴影效果
                if (u_shadowOffset > 0.0) {
                    vec2 shadowCoord = v_texCoord + vec2(u_shadowOffset) / vec2(textureSize(u_msdfTexture, 0));
                    vec3 shadowMsd = texture(u_msdfTexture, shadowCoord).rgb;
                    float shadowSd = median(shadowMsd.r, shadowMsd.g, shadowMsd.b);
                    float shadowAlpha = smoothstep(0.5 - u_smoothness, 0.5 + u_smoothness, shadowSd);
                    
                    vec4 shadowResult = mix(u_shadowColor, finalColor, finalColor.a);
                    shadowResult.a = max(shadowAlpha * u_shadowColor.a, finalColor.a);
                    finalColor = shadowResult;
                }
                
                fragColor = finalColor;
            }
        `;

        this.program = this.createProgram(vertexShaderSource, fragmentShaderSource);
        
        // 获取uniform和attribute位置
        this.uniforms = {
            projection: gl.getUniformLocation(this.program, 'u_projection'),
            modelView: gl.getUniformLocation(this.program, 'u_modelView'),
            msdfTexture: gl.getUniformLocation(this.program, 'u_msdfTexture'),
            color: gl.getUniformLocation(this.program, 'u_color'),
            pxRange: gl.getUniformLocation(this.program, 'u_pxRange'),
            outlineWidth: gl.getUniformLocation(this.program, 'u_outlineWidth'),
            outlineColor: gl.getUniformLocation(this.program, 'u_outlineColor'),
            shadowOffset: gl.getUniformLocation(this.program, 'u_shadowOffset'),
            shadowColor: gl.getUniformLocation(this.program, 'u_shadowColor'),
            smoothness: gl.getUniformLocation(this.program, 'u_smoothness')
        };

        this.attributes = {
            position: gl.getAttribLocation(this.program, 'a_position'),
            texCoord: gl.getAttribLocation(this.program, 'a_texCoord')
        };

        // 创建VAO
        this.vao = gl.createVertexArray();
        gl.bindVertexArray(this.vao);

        // 创建缓冲区
        this.vertexBuffer = gl.createBuffer();
        this.indexBuffer = gl.createBuffer();

        // 设置顶点属性
        gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
        gl.enableVertexAttribArray(this.attributes.position);
        gl.vertexAttribPointer(this.attributes.position, 2, gl.FLOAT, false, 16, 0);
        gl.enableVertexAttribArray(this.attributes.texCoord);
        gl.vertexAttribPointer(this.attributes.texCoord, 2, gl.FLOAT, false, 16, 8);

        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);

        // 启用混合
        gl.enable(gl.BLEND);
        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
    }

    createProgram(vertexSource, fragmentSource) {
        const gl = this.gl;
        
        const vertexShader = this.createShader(gl.VERTEX_SHADER, vertexSource);
        const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fragmentSource);
        
        const program = gl.createProgram();
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);
        
        if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
            throw new Error('Program link error: ' + gl.getProgramInfoLog(program));
        }
        
        return program;
    }

    createShader(type, source) {
        const gl = this.gl;
        const shader = gl.createShader(type);
        gl.shaderSource(shader, source);
        gl.compileShader(shader);
        
        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            throw new Error('Shader compile error: ' + gl.getShaderInfoLog(shader));
        }
        
        return shader;
    }

    // 创建纹理
    createTexture(image) {
        const gl = this.gl;
        const texture = gl.createTexture();
        
        gl.bindTexture(gl.TEXTURE_2D, texture);
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGB, gl.RGB, gl.UNSIGNED_BYTE, image);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
        
        return texture;
    }

    // 渲染文本
    renderText(geometry, texture, options = {}) {
        const gl = this.gl;
        
        const {
            projection = this.createOrthographicMatrix(0, gl.canvas.width, gl.canvas.height, 0, -1, 1),
            modelView = this.createIdentityMatrix(),
            color = [1, 1, 1, 1],
            pxRange = 4,
            outlineWidth = 0,
            outlineColor = [0, 0, 0, 1],
            shadowOffset = 0,
            shadowColor = [0, 0, 0, 0.5],
            smoothness = 0.1
        } = options;

        gl.useProgram(this.program);
        gl.bindVertexArray(this.vao);

        // 更新缓冲区数据
        gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, geometry.vertices, gl.DYNAMIC_DRAW);
        
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
        gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, geometry.indices, gl.DYNAMIC_DRAW);

        // 设置uniforms
        gl.uniformMatrix4fv(this.uniforms.projection, false, projection);
        gl.uniformMatrix4fv(this.uniforms.modelView, false, modelView);
        gl.uniform4fv(this.uniforms.color, color);
        gl.uniform1f(this.uniforms.pxRange, pxRange);
        gl.uniform1f(this.uniforms.outlineWidth, outlineWidth);
        gl.uniform4fv(this.uniforms.outlineColor, outlineColor);
        gl.uniform1f(this.uniforms.shadowOffset, shadowOffset);
        gl.uniform4fv(this.uniforms.shadowColor, shadowColor);
        gl.uniform1f(this.uniforms.smoothness, smoothness);

        // 绑定纹理
        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(gl.TEXTURE_2D, texture);
        gl.uniform1i(this.uniforms.msdfTexture, 0);

        // 绘制
        gl.drawElements(gl.TRIANGLES, geometry.indexCount, gl.UNSIGNED_SHORT, 0);
    }

    // 创建正交投影矩阵
    createOrthographicMatrix(left, right, bottom, top, near, far) {
        const lr = 1 / (left - right);
        const bt = 1 / (bottom - top);
        const nf = 1 / (near - far);
        
        return new Float32Array([
            -2 * lr, 0, 0, 0,
            0, -2 * bt, 0, 0,
            0, 0, 2 * nf, 0,
            (left + right) * lr, (top + bottom) * bt, (far + near) * nf, 1
        ]);
    }

    // 创建单位矩阵
    createIdentityMatrix() {
        return new Float32Array([
            1, 0, 0, 0,
            0, 1, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        ]);
    }
}

// 使用示例
/*
// 初始化
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl2');
const fontLoader = new MSDFFontLoader();
const renderer = new MSDFTextRenderer(gl);

// 加载字体
await fontLoader.loadFont('chinese', 'chinese-msdf.png', 'chinese-metrics.json');

// 生成文本几何
const geometry = fontLoader.generateTextGeometry('chinese', '你好世界', 48);

// 创建纹理
const font = fontLoader.getFont('chinese');
const texture = renderer.createTexture(font.atlas);

// 渲染
renderer.renderText(geometry, texture, {
    color: [1, 1, 1, 1],
    outlineWidth: 0.1,
    outlineColor: [0, 0, 0, 1]
});
*/
