<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSDF数据转Canvas 2D示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #4CAF50;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .canvas-section {
            text-align: center;
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
        }
        
        .canvas-section h3 {
            margin: 0 0 15px 0;
            color: #ccc;
        }
        
        canvas {
            border: 1px solid #333;
            background: #000;
            border-radius: 4px;
        }
        
        .controls {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            line-height: 1.6;
        }
        
        .code-block {
            background: #444;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .status {
            text-align: center;
            padding: 10px;
            background: #444;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MSDF数据转Canvas 2D渲染示例</h1>
        
        <div class="comparison">
            <div class="canvas-section">
                <h3>WebGL MSDF渲染</h3>
                <canvas id="webglCanvas" width="400" height="200"></canvas>
            </div>
            <div class="canvas-section">
                <h3>Canvas 2D渲染</h3>
                <canvas id="canvas2d" width="400" height="200"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="renderMSDFText()">渲染MSDF文本</button>
            <button onclick="renderWithTexture()">带纹理渲染</button>
            <button onclick="renderWireframe()">线框模式</button>
            <button onclick="clearCanvases()">清除画布</button>
        </div>
        
        <div class="status" id="status">点击按钮开始演示</div>
        
        <div class="info">
            <h3>核心函数使用示例</h3>
            <div class="code-block">
// 基本用法
drawInCanvas2DByGlData(ctx, vertices, indices);

// 带选项的用法
drawInCanvas2DByGlData(ctx, vertices, indices, {
    fillStyle: '#ff6b6b',
    strokeStyle: '#fff',
    lineWidth: 2,
    texture: textureImage,
    alpha: 0.8
});

// 线框模式
drawWireframe(ctx, vertices, indices, {
    strokeStyle: '#00ff00',
    lineWidth: 1
});
            </div>
        </div>
        
        <div class="info">
            <h3>支持的顶点格式</h3>
            <ul>
                <li><strong>位置数据</strong>: [x, y] - 步长2</li>
                <li><strong>位置+纹理</strong>: [x, y, u, v] - 步长4</li>
                <li><strong>位置+颜色</strong>: [x, y, r, g, b, a] - 步长6</li>
                <li><strong>完整格式</strong>: [x, y, u, v, r, g, b, a] - 步长8</li>
            </ul>
            <p>函数会自动检测顶点格式并相应处理。</p>
        </div>
    </div>

    <script type="module">
        import { drawInCanvas2DByGlData, drawWireframe, drawVertices } from './canvas2d-gl-renderer.js';
        import { generateTextGeometry, loadFontData } from './msdf-demo.js';
        
        // 获取画布上下文
        const webglCanvas = document.getElementById('webglCanvas');
        const webglCtx = webglCanvas.getContext('2d'); // 用2D模拟WebGL
        const canvas2d = document.getElementById('canvas2d');
        const ctx2d = canvas2d.getContext('2d');
        
        let fontData = null;
        let currentVertices = null;
        let currentIndices = null;
        
        // 状态更新
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        // 初始化字体数据
        async function initFont() {
            try {
                fontData = await loadFontData();
                updateStatus('字体数据加载完成');
                return true;
            } catch (error) {
                updateStatus('字体数据加载失败: ' + error.message);
                return false;
            }
        }
        
        // 清除画布
        function clearCanvases() {
            webglCtx.clearRect(0, 0, webglCanvas.width, webglCanvas.height);
            ctx2d.clearRect(0, 0, canvas2d.width, canvas2d.height);
            updateStatus('画布已清除');
        }
        
        // 模拟WebGL MSDF渲染
        function simulateWebGLRender(text, x, y, fontSize) {
            webglCtx.save();
            webglCtx.fillStyle = '#4CAF50';
            webglCtx.font = `${fontSize}px Arial, sans-serif`;
            webglCtx.fillText(text, x, y);
            webglCtx.restore();
        }
        
        // 渲染MSDF文本
        async function renderMSDFText() {
            clearCanvases();
            
            if (!fontData) {
                const success = await initFont();
                if (!success) return;
            }
            
            try {
                const text = '你好';
                const fontSize = 48;
                
                // 生成MSDF几何数据
                const geometry = generateTextGeometry(text, fontData, fontSize);
                currentVertices = geometry.vertices;
                currentIndices = geometry.indices;
                
                // 模拟WebGL渲染
                simulateWebGLRender(text, 50, 100, fontSize);
                
                // Canvas 2D渲染
                drawInCanvas2DByGlData(ctx2d, currentVertices, currentIndices, {
                    fillStyle: '#ff6b6b',
                    strokeStyle: '#fff',
                    lineWidth: 1
                });
                
                updateStatus(`渲染完成: "${text}" (${currentVertices.length/4}个顶点, ${currentIndices.length/3}个三角形)`);
                
            } catch (error) {
                updateStatus('渲染失败: ' + error.message);
                console.error(error);
            }
        }
        
        // 带纹理渲染
        function renderWithTexture() {
            if (!currentVertices || !currentIndices) {
                updateStatus('请先生成MSDF文本数据');
                return;
            }
            
            clearCanvases();
            
            // 创建简单纹理
            const textureCanvas = document.createElement('canvas');
            textureCanvas.width = 64;
            textureCanvas.height = 64;
            const textureCtx = textureCanvas.getContext('2d');
            
            // 渐变纹理
            const gradient = textureCtx.createLinearGradient(0, 0, 64, 64);
            gradient.addColorStop(0, '#ff6b6b');
            gradient.addColorStop(0.5, '#4ecdc4');
            gradient.addColorStop(1, '#45b7d1');
            
            textureCtx.fillStyle = gradient;
            textureCtx.fillRect(0, 0, 64, 64);
            
            // 模拟WebGL渲染
            simulateWebGLRender('你好', 50, 100, 48);
            
            // Canvas 2D纹理渲染
            drawInCanvas2DByGlData(ctx2d, currentVertices, currentIndices, {
                texture: textureCanvas,
                strokeStyle: '#fff',
                lineWidth: 2
            });
            
            updateStatus('纹理渲染完成');
        }
        
        // 线框模式
        function renderWireframe() {
            if (!currentVertices || !currentIndices) {
                updateStatus('请先生成MSDF文本数据');
                return;
            }
            
            clearCanvases();
            
            // 模拟WebGL线框
            webglCtx.strokeStyle = '#4CAF50';
            webglCtx.lineWidth = 2;
            webglCtx.strokeRect(50, 60, 100, 50);
            webglCtx.strokeRect(160, 60, 100, 50);
            
            // Canvas 2D线框渲染
            drawWireframe(ctx2d, currentVertices, currentIndices, {
                strokeStyle: '#00ff00',
                lineWidth: 2
            });
            
            // 绘制顶点
            drawVertices(ctx2d, currentVertices, {
                radius: 3,
                fillStyle: '#ff0000'
            });
            
            updateStatus('线框模式渲染完成');
        }
        
        // 全局函数
        window.renderMSDFText = renderMSDFText;
        window.renderWithTexture = renderWithTexture;
        window.renderWireframe = renderWireframe;
        window.clearCanvases = clearCanvases;
        
        // 初始化
        updateStatus('准备就绪，点击按钮开始演示');
    </script>
</body>
</html>
