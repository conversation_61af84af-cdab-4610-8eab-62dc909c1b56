import { signal, effect } from './signal'

console.log('=== 条件Effect精确测试 ===')

const isVisible = signal(true)
const content = signal('内容A')
const otherData = signal('其他数据')

let effectRunCount = 0
const cleanup = effect(() => {
  effectRunCount++
  console.log(`\n[运行 ${effectRunCount}]`)
  
  if (isVisible.get()) {
    console.log(`显示: ${content.get()}`)
  } else {
    console.log('隐藏状态')
    // 注意：这里没有访问content，所以不应该依赖content
  }
})

console.log('初始运行次数:', effectRunCount) // 1

setTimeout(() => {
  console.log('\n--- 修改content (应该触发，因为当前可见) ---')
  content.set('内容B')
  
  setTimeout(() => {
    console.log('修改content后运行次数:', effectRunCount) // 2
    
    console.log('\n--- 修改otherData (不应该触发，因为没有依赖) ---')
    otherData.set('其他数据修改')
    
    setTimeout(() => {
      console.log('修改otherData后运行次数:', effectRunCount) // 还是2
      
      console.log('\n--- 设置为隐藏 ---')
      isVisible.set(false)
      
      setTimeout(() => {
        console.log('设置隐藏后运行次数:', effectRunCount) // 3
        
        console.log('\n--- 隐藏状态下修改content (不应该触发，因为没有访问) ---')
        content.set('内容C')
        
        setTimeout(() => {
          console.log('隐藏状态修改content后运行次数:', effectRunCount) // 还是3
          
          console.log('\n--- 重新显示 ---')
          isVisible.set(true)
          
          setTimeout(() => {
            console.log('重新显示后运行次数:', effectRunCount) // 4，会显示最新的内容C
            
            cleanup()
            console.log('\n=== 条件Effect测试完成 ===')
          }, 10)
        }, 10)
      }, 10)
    }, 10)
  }, 10)
}, 10)
