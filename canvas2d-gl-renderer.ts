// Canvas 2D渲染器 - 基于WebGL顶点和索引数据
// 将WebGL几何数据转换为Canvas 2D绘制

// 顶点数据格式定义
interface Vertex {
  x: number;
  y: number;
  u?: number;  // 纹理坐标U (可选)
  v?: number;  // 纹理坐标V (可选)
  r?: number;  // 颜色R (可选)
  g?: number;  // 颜色G (可选)
  b?: number;  // 颜色B (可选)
  a?: number;  // 透明度A (可选)
}

// 渲染选项
interface RenderOptions {
  fillStyle?: string | CanvasGradient | CanvasPattern;
  strokeStyle?: string | CanvasGradient | CanvasPattern;
  lineWidth?: number;
  wireframe?: boolean;
  texture?: HTMLImageElement | HTMLCanvasElement;
  transform?: DOMMatrix;
  alpha?: number;
}

// 解析顶点数据
const parseVertices = (vertices: Float32Array, stride: number = 4): Vertex[] => {
  const result: Vertex[] = [];
  
  for (let i = 0; i < vertices.length; i += stride) {
    const vertex: Vertex = {
      x: vertices[i],
      y: vertices[i + 1]
    };
    
    // 根据步长解析额外数据
    if (stride >= 4) {
      vertex.u = vertices[i + 2];
      vertex.v = vertices[i + 3];
    }
    if (stride >= 6) {
      vertex.r = vertices[i + 4];
      vertex.g = vertices[i + 5];
    }
    if (stride >= 7) {
      vertex.b = vertices[i + 6];
    }
    if (stride >= 8) {
      vertex.a = vertices[i + 7];
    }
    
    result.push(vertex);
  }
  
  return result;
};

// 绘制三角形
const drawTriangle = (
  ctx: CanvasRenderingContext2D,
  v1: Vertex,
  v2: Vertex,
  v3: Vertex,
  options: RenderOptions = {}
) => {
  ctx.save();
  
  // 应用变换
  if (options.transform) {
    ctx.setTransform(options.transform);
  }
  
  // 设置透明度
  if (options.alpha !== undefined) {
    ctx.globalAlpha = options.alpha;
  }
  
  // 创建路径
  ctx.beginPath();
  ctx.moveTo(v1.x, v1.y);
  ctx.lineTo(v2.x, v2.y);
  ctx.lineTo(v3.x, v3.y);
  ctx.closePath();
  
  // 填充
  if (!options.wireframe && options.fillStyle) {
    ctx.fillStyle = options.fillStyle;
    ctx.fill();
  }
  
  // 描边
  if (options.strokeStyle) {
    ctx.strokeStyle = options.strokeStyle;
    ctx.lineWidth = options.lineWidth || 1;
    ctx.stroke();
  }
  
  ctx.restore();
};

// 绘制纹理三角形
const drawTexturedTriangle = (
  ctx: CanvasRenderingContext2D,
  v1: Vertex,
  v2: Vertex,
  v3: Vertex,
  texture: HTMLImageElement | HTMLCanvasElement,
  options: RenderOptions = {}
) => {
  if (!v1.u || !v1.v || !v2.u || !v2.v || !v3.u || !v3.v) {
    // 如果没有纹理坐标，回退到普通绘制
    drawTriangle(ctx, v1, v2, v3, options);
    return;
  }
  
  ctx.save();
  
  // 应用变换
  if (options.transform) {
    ctx.setTransform(options.transform);
  }
  
  // 设置透明度
  if (options.alpha !== undefined) {
    ctx.globalAlpha = options.alpha;
  }
  
  // 创建裁剪路径
  ctx.beginPath();
  ctx.moveTo(v1.x, v1.y);
  ctx.lineTo(v2.x, v2.y);
  ctx.lineTo(v3.x, v3.y);
  ctx.closePath();
  ctx.clip();
  
  // 计算纹理变换矩阵
  const textureWidth = texture.width || (texture as HTMLCanvasElement).width;
  const textureHeight = texture.height || (texture as HTMLCanvasElement).height;
  
  // 简化的纹理映射 - 使用仿射变换
  try {
    // 计算纹理坐标到屏幕坐标的变换
    const tx1 = v1.u * textureWidth;
    const ty1 = v1.v * textureHeight;
    const tx2 = v2.u * textureWidth;
    const ty2 = v2.v * textureHeight;
    const tx3 = v3.u * textureWidth;
    const ty3 = v3.v * textureHeight;
    
    // 使用第一个三角形进行简单的纹理映射
    const sx = (v2.x - v1.x) / (tx2 - tx1) || 1;
    const sy = (v2.y - v1.y) / (ty2 - ty1) || 1;
    
    ctx.scale(sx, sy);
    ctx.translate(-tx1 + v1.x / sx, -ty1 + v1.y / sy);
    
    ctx.drawImage(texture, 0, 0);
  } catch (e) {
    // 如果纹理映射失败，绘制纯色三角形
    ctx.fillStyle = options.fillStyle || '#888';
    ctx.fill();
  }
  
  // 描边
  if (options.strokeStyle) {
    ctx.strokeStyle = options.strokeStyle;
    ctx.lineWidth = options.lineWidth || 1;
    ctx.stroke();
  }
  
  ctx.restore();
};

// 主要的绘制函数
const drawInCanvas2DByGlData = (
  ctx: CanvasRenderingContext2D,
  vertices: Float32Array,
  indices: Uint16Array | Uint32Array,
  options: RenderOptions = {}
) => {
  // 自动检测顶点数据格式
  const vertexCount = vertices.length;
  let stride = 2; // 默认只有位置数据 (x, y)
  
  // 根据常见的顶点格式推断步长
  if (vertexCount % 4 === 0) stride = 4; // 位置 + 纹理坐标 (x, y, u, v)
  else if (vertexCount % 6 === 0) stride = 6; // 位置 + 颜色 (x, y, r, g, b, a)
  else if (vertexCount % 8 === 0) stride = 8; // 位置 + 纹理 + 颜色
  
  // 解析顶点数据
  const parsedVertices = parseVertices(vertices, stride);
  
  // 确保索引数量是3的倍数（三角形）
  if (indices.length % 3 !== 0) {
    console.warn('索引数量不是3的倍数，可能不是三角形数据');
  }
  
  // 绘制每个三角形
  for (let i = 0; i < indices.length; i += 3) {
    const i1 = indices[i];
    const i2 = indices[i + 1];
    const i3 = indices[i + 2];
    
    // 检查索引有效性
    if (i1 >= parsedVertices.length || i2 >= parsedVertices.length || i3 >= parsedVertices.length) {
      console.warn(`无效的索引: ${i1}, ${i2}, ${i3}`);
      continue;
    }
    
    const v1 = parsedVertices[i1];
    const v2 = parsedVertices[i2];
    const v3 = parsedVertices[i3];
    
    // 如果有纹理且顶点包含纹理坐标，使用纹理绘制
    if (options.texture && v1.u !== undefined) {
      drawTexturedTriangle(ctx, v1, v2, v3, options.texture, options);
    } else {
      // 使用顶点颜色或默认颜色
      let fillStyle = options.fillStyle;
      
      // 如果顶点包含颜色信息，使用顶点颜色
      if (v1.r !== undefined && v1.g !== undefined && v1.b !== undefined) {
        const r = Math.floor((v1.r + v2.r + v3.r) / 3 * 255);
        const g = Math.floor((v1.g + v2.g + v3.g) / 3 * 255);
        const b = Math.floor((v1.b + v2.b + v3.b) / 3 * 255);
        const a = v1.a !== undefined ? (v1.a + v2.a + v3.a) / 3 : 1;
        fillStyle = `rgba(${r}, ${g}, ${b}, ${a})`;
      }
      
      drawTriangle(ctx, v1, v2, v3, { ...options, fillStyle });
    }
  }
};

// 辅助函数：绘制线框
const drawWireframe = (
  ctx: CanvasRenderingContext2D,
  vertices: Float32Array,
  indices: Uint16Array | Uint32Array,
  options: RenderOptions = {}
) => {
  drawInCanvas2DByGlData(ctx, vertices, indices, {
    ...options,
    wireframe: true,
    fillStyle: undefined,
    strokeStyle: options.strokeStyle || '#00ff00',
    lineWidth: options.lineWidth || 1
  });
};

// 辅助函数：绘制顶点点
const drawVertices = (
  ctx: CanvasRenderingContext2D,
  vertices: Float32Array,
  options: { radius?: number; fillStyle?: string } = {}
) => {
  const radius = options.radius || 3;
  const fillStyle = options.fillStyle || '#ff0000';
  
  ctx.save();
  ctx.fillStyle = fillStyle;
  
  // 假设步长为4 (x, y, u, v)
  for (let i = 0; i < vertices.length; i += 4) {
    const x = vertices[i];
    const y = vertices[i + 1];
    
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fill();
  }
  
  ctx.restore();
};

// 辅助函数：创建变换矩阵
const createTransform = (
  translateX: number = 0,
  translateY: number = 0,
  scaleX: number = 1,
  scaleY: number = 1,
  rotation: number = 0
): DOMMatrix => {
  const matrix = new DOMMatrix();
  matrix.translateSelf(translateX, translateY);
  matrix.rotateSelf(rotation * 180 / Math.PI);
  matrix.scaleSelf(scaleX, scaleY);
  return matrix;
};

// 导出类型
export type { Vertex, RenderOptions };

// 导出函数
export {
  drawInCanvas2DByGlData,
  drawWireframe,
  drawVertices,
  createTransform
};
