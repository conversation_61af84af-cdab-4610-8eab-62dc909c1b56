<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSDFAtlas数据 → WebGL2渲染演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
        }
        
        .workflow {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        .workflow h3 {
            color: #4CAF50;
            margin-bottom: 15px;
        }
        
        .step {
            display: inline-block;
            background: #333;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .arrow {
            color: #4CAF50;
            font-size: 20px;
            margin: 0 10px;
        }
        
        .controls {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .control-row {
            display: flex;
            align-items: center;
            margin: 15px 0;
            gap: 15px;
        }
        
        .control-row label {
            min-width: 120px;
            color: #ccc;
        }
        
        .control-row input[type="text"] {
            flex: 1;
            max-width: 300px;
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            color: white;
            border-radius: 4px;
        }
        
        .control-row input[type="range"] {
            flex: 1;
            max-width: 200px;
        }
        
        .control-row input[type="color"] {
            width: 50px;
            height: 35px;
            border: none;
            border-radius: 4px;
        }
        
        .value {
            min-width: 80px;
            color: #fff;
            font-weight: bold;
            font-family: monospace;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        button {
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .canvas-section {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .canvas-wrapper {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .canvas-wrapper h3 {
            margin: 0 0 15px 0;
            color: #ccc;
        }
        
        canvas {
            border: 1px solid #333;
            background: #000;
            border-radius: 4px;
        }
        
        .status {
            background: #444;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            text-align: center;
        }
        
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .atlas-info {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .atlas-info h3 {
            color: #4CAF50;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MSDFAtlas数据 → WebGL2渲染演示</h1>
        
        <div class="workflow">
            <h3>数据流程</h3>
            <span class="step">generateMSDFAtlas()</span>
            <span class="arrow">→</span>
            <span class="step">MSDFAtlas数据</span>
            <span class="arrow">→</span>
            <span class="step">WebGL2纹理</span>
            <span class="arrow">→</span>
            <span class="step">MSDF渲染</span>
        </div>
        
        <div class="controls">
            <h3>1. 生成MSDF图集数据</h3>
            
            <div class="control-row">
                <label>字符内容:</label>
                <input type="text" id="charactersInput" value="你好世界WebGL2" placeholder="输入要生成的字符">
            </div>
            
            <div class="control-row">
                <label>字体大小:</label>
                <input type="range" id="fontSizeSlider" min="32" max="96" value="64">
                <span class="value" id="fontSizeValue">64px</span>
            </div>
            
            <div class="button-group">
                <button onclick="generateAtlas()">生成MSDF图集</button>
                <button onclick="loadPresetChinese()">中文预设</button>
                <button onclick="loadPresetEnglish()">英文预设</button>
            </div>
        </div>
        
        <div class="controls">
            <h3>2. WebGL2渲染参数</h3>
            
            <div class="control-row">
                <label>渲染文本:</label>
                <input type="text" id="renderTextInput" value="你好WebGL2" placeholder="输入要渲染的文本">
            </div>
            
            <div class="control-row">
                <label>渲染大小:</label>
                <input type="range" id="renderSizeSlider" min="20" max="120" value="48">
                <span class="value" id="renderSizeValue">48px</span>
            </div>
            
            <div class="control-row">
                <label>X位置:</label>
                <input type="range" id="xSlider" min="0" max="400" value="50">
                <span class="value" id="xValue">50</span>
            </div>
            
            <div class="control-row">
                <label>Y位置:</label>
                <input type="range" id="ySlider" min="0" max="300" value="100">
                <span class="value" id="yValue">100</span>
            </div>
            
            <div class="control-row">
                <label>文本颜色:</label>
                <input type="color" id="colorPicker" value="#ffffff">
            </div>
            
            <div class="control-row">
                <label>描边宽度:</label>
                <input type="range" id="outlineSlider" min="0" max="0.2" step="0.01" value="0.05">
                <span class="value" id="outlineValue">0.05</span>
            </div>
            
            <div class="control-row">
                <label>描边颜色:</label>
                <input type="color" id="outlineColorPicker" value="#ff0000">
            </div>
            
            <div class="button-group">
                <button onclick="renderText()" id="renderButton" disabled>渲染文本</button>
                <button onclick="animateText()">动画演示</button>
                <button onclick="clearCanvas()">清除画布</button>
            </div>
        </div>
        
        <div class="status" id="status">请先生成MSDF图集数据</div>
        
        <div class="canvas-section">
            <div class="canvas-wrapper">
                <h3>生成的MSDF图集</h3>
                <canvas id="atlasCanvas" width="300" height="300"></canvas>
            </div>
            <div class="canvas-wrapper">
                <h3>WebGL2 MSDF渲染</h3>
                <canvas id="webglCanvas" width="500" height="300"></canvas>
            </div>
        </div>
        
        <div class="atlas-info" id="atlasInfo" style="display: none;">
            <h3>图集信息</h3>
            <div id="atlasDetails">等待生成...</div>
        </div>
        
        <div class="info">
            <h3>技术说明</h3>
            <ul>
                <li><strong>generateMSDFAtlas()</strong>: 使用Canvas 2D生成MSDF图集数据</li>
                <li><strong>createTextureFromAtlas()</strong>: 将图集Canvas转换为WebGL2纹理</li>
                <li><strong>generateTextGeometryFromAtlas()</strong>: 基于图集数据生成文本几何</li>
                <li><strong>renderTextWithAtlas()</strong>: 使用WebGL2 MSDF着色器渲染</li>
                <li><strong>实时参数</strong>: 支持颜色、大小、位置、描边等实时调整</li>
            </ul>
        </div>
    </div>

    <script type="module">
        import { generateMSDFAtlas } from './msdf-generator-canvas2d.js';
        import { createMSDFRendererFromAtlas } from './webgl2-msdf-renderer.js';
        
        // 控制元素
        const charactersInput = document.getElementById('charactersInput');
        const fontSizeSlider = document.getElementById('fontSizeSlider');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const renderTextInput = document.getElementById('renderTextInput');
        const renderSizeSlider = document.getElementById('renderSizeSlider');
        const renderSizeValue = document.getElementById('renderSizeValue');
        const xSlider = document.getElementById('xSlider');
        const xValue = document.getElementById('xValue');
        const ySlider = document.getElementById('ySlider');
        const yValue = document.getElementById('yValue');
        const colorPicker = document.getElementById('colorPicker');
        const outlineSlider = document.getElementById('outlineSlider');
        const outlineValue = document.getElementById('outlineValue');
        const outlineColorPicker = document.getElementById('outlineColorPicker');
        const renderButton = document.getElementById('renderButton');
        
        // 画布
        const atlasCanvas = document.getElementById('atlasCanvas');
        const webglCanvas = document.getElementById('webglCanvas');
        const atlasCtx = atlasCanvas.getContext('2d');
        
        let currentAtlas = null;
        let webglRenderer = null;
        
        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log(message);
        }
        
        // 更新滑块值
        function updateSliderValues() {
            fontSizeValue.textContent = fontSizeSlider.value + 'px';
            renderSizeValue.textContent = renderSizeSlider.value + 'px';
            xValue.textContent = xSlider.value;
            yValue.textContent = ySlider.value;
            outlineValue.textContent = outlineSlider.value;
        }
        
        // 生成MSDF图集
        function generateAtlas() {
            try {
                updateStatus('正在生成MSDF图集...');
                
                const characters = Array.from(new Set(charactersInput.value.split(''))).filter(c => c.trim());
                const fontSize = parseInt(fontSizeSlider.value);
                
                if (characters.length === 0) {
                    updateStatus('错误: 请输入字符');
                    return;
                }
                
                const config = {
                    fontSize: fontSize,
                    padding: 8,
                    distanceRange: 4,
                    atlasWidth: 512,
                    atlasHeight: 512,
                    fontFamily: 'SimHei, Arial, sans-serif'
                };
                
                // 生成图集
                currentAtlas = generateMSDFAtlas(characters, config);
                
                // 显示图集
                showAtlas(currentAtlas);
                
                // 创建WebGL2渲染器
                createWebGLRenderer(currentAtlas);
                
                // 更新信息
                updateAtlasInfo(currentAtlas);
                
                // 启用渲染按钮
                renderButton.disabled = false;
                
                updateStatus(`图集生成完成! 包含${characters.length}个字符`);
                
            } catch (error) {
                updateStatus('生成失败: ' + error.message);
                console.error(error);
            }
        }
        
        // 显示图集
        function showAtlas(atlas) {
            atlasCtx.clearRect(0, 0, atlasCanvas.width, atlasCanvas.height);
            
            const scale = Math.min(
                atlasCanvas.width / atlas.canvas.width,
                atlasCanvas.height / atlas.canvas.height
            );
            
            const scaledWidth = atlas.canvas.width * scale;
            const scaledHeight = atlas.canvas.height * scale;
            const offsetX = (atlasCanvas.width - scaledWidth) / 2;
            const offsetY = (atlasCanvas.height - scaledHeight) / 2;
            
            atlasCtx.drawImage(atlas.canvas, offsetX, offsetY, scaledWidth, scaledHeight);
        }
        
        // 创建WebGL2渲染器
        function createWebGLRenderer(atlas) {
            try {
                if (webglRenderer) {
                    webglRenderer.dispose();
                }
                
                webglRenderer = createMSDFRendererFromAtlas(webglCanvas, atlas);
                updateStatus('WebGL2渲染器创建成功');
                
            } catch (error) {
                updateStatus('WebGL2渲染器创建失败: ' + error.message);
                console.error(error);
            }
        }
        
        // 渲染文本
        function renderText() {
            if (!webglRenderer) {
                updateStatus('请先生成MSDF图集');
                return;
            }
            
            try {
                const text = renderTextInput.value;
                const fontSize = parseInt(renderSizeSlider.value);
                const x = parseInt(xSlider.value);
                const y = parseInt(ySlider.value);
                const outlineWidth = parseFloat(outlineSlider.value);
                
                // 转换颜色
                const colorHex = colorPicker.value;
                const color = [
                    parseInt(colorHex.substr(1, 2), 16) / 255,
                    parseInt(colorHex.substr(3, 2), 16) / 255,
                    parseInt(colorHex.substr(5, 2), 16) / 255
                ];
                
                const outlineColorHex = outlineColorPicker.value;
                const outlineColor = [
                    parseInt(outlineColorHex.substr(1, 2), 16) / 255,
                    parseInt(outlineColorHex.substr(3, 2), 16) / 255,
                    parseInt(outlineColorHex.substr(5, 2), 16) / 255
                ];
                
                // 渲染
                webglRenderer.render(text, {
                    x: x,
                    y: y,
                    fontSize: fontSize,
                    color: color,
                    outlineColor: outlineColor,
                    outlineWidth: outlineWidth,
                    smoothness: 0.05
                });
                
                updateStatus(`渲染完成: "${text}"`);
                
            } catch (error) {
                updateStatus('渲染失败: ' + error.message);
                console.error(error);
            }
        }
        
        // 动画演示
        function animateText() {
            if (!webglRenderer) return;
            
            let frame = 0;
            const animate = () => {
                const time = frame * 0.05;
                const x = 50 + Math.sin(time) * 100;
                const y = 150 + Math.cos(time * 0.7) * 50;
                const fontSize = 40 + Math.sin(time * 2) * 20;
                
                const hue = (frame * 2) % 360;
                const color = [
                    Math.sin(hue * Math.PI / 180 * 2) * 0.5 + 0.5,
                    Math.sin((hue + 120) * Math.PI / 180 * 2) * 0.5 + 0.5,
                    Math.sin((hue + 240) * Math.PI / 180 * 2) * 0.5 + 0.5
                ];
                
                webglRenderer.render('动画MSDF', {
                    x: x,
                    y: y,
                    fontSize: fontSize,
                    color: color,
                    outlineColor: [0, 0, 0],
                    outlineWidth: 0.1,
                    smoothness: 0.05
                });
                
                frame++;
                if (frame < 300) {
                    requestAnimationFrame(animate);
                } else {
                    updateStatus('动画演示完成');
                }
            };
            
            updateStatus('播放动画演示...');
            animate();
        }
        
        // 清除画布
        function clearCanvas() {
            if (webglRenderer) {
                const gl = webglRenderer.context.gl;
                gl.clearColor(0.1, 0.1, 0.1, 1);
                gl.clear(gl.COLOR_BUFFER_BIT);
            }
            updateStatus('画布已清除');
        }
        
        // 更新图集信息
        function updateAtlasInfo(atlas) {
            const info = `
字符数量: ${atlas.characters.size}
图集尺寸: ${atlas.config.atlasWidth}x${atlas.config.atlasHeight}
字体大小: ${atlas.config.fontSize}px
距离范围: ${atlas.config.distanceRange}
支持字符: ${Array.from(atlas.characters.keys()).join(' ')}
            `.trim();
            
            document.getElementById('atlasDetails').textContent = info;
            document.getElementById('atlasInfo').style.display = 'block';
        }
        
        // 预设
        function loadPresetChinese() {
            charactersInput.value = '你好世界中文WebGL2渲染测试';
            renderTextInput.value = '你好WebGL2';
        }
        
        function loadPresetEnglish() {
            charactersInput.value = 'Hello World WebGL2 MSDF Rendering';
            renderTextInput.value = 'Hello WebGL2';
        }
        
        // 事件监听
        fontSizeSlider.addEventListener('input', updateSliderValues);
        renderSizeSlider.addEventListener('input', updateSliderValues);
        xSlider.addEventListener('input', updateSliderValues);
        ySlider.addEventListener('input', updateSliderValues);
        outlineSlider.addEventListener('input', updateSliderValues);
        
        // 实时渲染
        renderTextInput.addEventListener('input', () => {
            if (webglRenderer) renderText();
        });
        renderSizeSlider.addEventListener('input', () => {
            updateSliderValues();
            if (webglRenderer) renderText();
        });
        xSlider.addEventListener('input', () => {
            updateSliderValues();
            if (webglRenderer) renderText();
        });
        ySlider.addEventListener('input', () => {
            updateSliderValues();
            if (webglRenderer) renderText();
        });
        colorPicker.addEventListener('input', () => {
            if (webglRenderer) renderText();
        });
        outlineSlider.addEventListener('input', () => {
            updateSliderValues();
            if (webglRenderer) renderText();
        });
        outlineColorPicker.addEventListener('input', () => {
            if (webglRenderer) renderText();
        });
        
        // 全局函数
        window.generateAtlas = generateAtlas;
        window.renderText = renderText;
        window.animateText = animateText;
        window.clearCanvas = clearCanvas;
        window.loadPresetChinese = loadPresetChinese;
        window.loadPresetEnglish = loadPresetEnglish;
        
        // 初始化
        updateSliderValues();
        updateStatus('准备就绪，请先生成MSDF图集');
    </script>
</body>
</html>
