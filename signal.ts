// Signal机制实现
type Subscriber<T> = (value: T) => void
type Unsubscribe = () => void
type ComputeFn<T> = () => T
type UpdateFn<T> = (prev: T) => T

// 全局状态管理
let currentComputation: ComputedSignalImpl<any> | null = null
let batchDepth = 0
let batchedUpdates = new Set<() => void>()

// 基础Signal接口
export interface ReadonlySignal<T> {
  get(): T
  subscribe(fn: Subscriber<T>): Unsubscribe
}

export interface WritableSignal<T> extends ReadonlySignal<T> {
  set(value: T): void
  update(fn: UpdateFn<T>): void
}

export interface ComputedSignal<T> extends ReadonlySignal<T> {}

// 可写Signal实现
class WritableSignalImpl<T> implements WritableSignal<T> {
  private _value: T
  private _subscribers = new Set<Subscriber<T>>()

  constructor(initialValue: T) {
    this._value = initialValue
  }

  get(): T {
    // 如果在计算过程中被访问，建立依赖关系
    if (currentComputation) {
      currentComputation.addDependency(this)
    }
    return this._value
  }

  set(value: T): void {
    if (this._value !== value) {
      this._value = value
      this._notify()
    }
  }

  update(fn: UpdateFn<T>): void {
    this.set(fn(this._value))
  }

  subscribe(fn: Subscriber<T>): Unsubscribe {
    this._subscribers.add(fn)
    return () => this._subscribers.delete(fn)
  }

  private _notify(): void {
    if (batchDepth > 0) {
      batchedUpdates.add(() => this._notifySubscribers())
    } else {
      this._notifySubscribers()
    }
  }

  private _notifySubscribers(): void {
    this._subscribers.forEach(fn => fn(this._value))
  }
}

// 计算Signal实现
class ComputedSignalImpl<T> implements ComputedSignal<T> {
  private _value: T | undefined
  private _computed = false
  private _dependencies = new Set<ReadonlySignal<any>>()
  private _subscribers = new Set<Subscriber<T>>()
  private _unsubscribers = new Set<Unsubscribe>()

  constructor(private _computeFn: ComputeFn<T>) {}

  get(): T {
    if (!this._computed) {
      this._compute()
    }

    // 建立依赖关系
    if (currentComputation) {
      currentComputation.addDependency(this)
    }

    return this._value!
  }

  subscribe(fn: Subscriber<T>): Unsubscribe {
    this._subscribers.add(fn)
    return () => this._subscribers.delete(fn)
  }

  addDependency(signal: ReadonlySignal<any>): void {
    if (!this._dependencies.has(signal)) {
      this._dependencies.add(signal)
      const unsubscribe = signal.subscribe(() => this._invalidate())
      this._unsubscribers.add(unsubscribe)
    }
  }

  private _compute(): void {
    const prevComputation = currentComputation
    currentComputation = this

    try {
      this._value = this._computeFn()
      this._computed = true
    } finally {
      currentComputation = prevComputation
    }
  }

  private _invalidate(): void {
    if (this._computed) {
      this._computed = false

      if (batchDepth > 0) {
        batchedUpdates.add(() => this._notifySubscribers())
      } else {
        this._notifySubscribers()
      }
    }
  }

  private _notifySubscribers(): void {
    this._subscribers.forEach(fn => fn(this.get()))
  }
}

// 只读Signal包装器
class ReadonlySignalImpl<T> implements ReadonlySignal<T> {
  constructor(private _signal: WritableSignal<T>) {}

  get(): T {
    return this._signal.get()
  }

  subscribe(fn: Subscriber<T>): Unsubscribe {
    return this._signal.subscribe(fn)
  }
}

// 创建函数
export function signal<T>(initialValue: T): WritableSignal<T> {
  return new WritableSignalImpl(initialValue)
}

export function computed<T>(computeFn: ComputeFn<T>): ComputedSignal<T> {
  return new ComputedSignalImpl(computeFn)
}

export function readonly<T>(signal: WritableSignal<T>): ReadonlySignal<T> {
  return new ReadonlySignalImpl(signal)
}

// 工具函数
export function batch(fn: () => void): void {
  batchDepth++
  try {
    fn()
  } finally {
    batchDepth--
    if (batchDepth === 0) {
      const updates = Array.from(batchedUpdates)
      batchedUpdates.clear()
      updates.forEach(update => update())
    }
  }
}

export function effect(fn: () => void): Unsubscribe {
  let dependencies = new Set<ReadonlySignal<any>>()
  let unsubscribers = new Set<Unsubscribe>()
  let isDestroyed = false
  let isRunning = false

  const cleanup = () => {
    isDestroyed = true
    unsubscribers.forEach(unsub => unsub())
    unsubscribers.clear()
    dependencies.clear()
  }

  const run = () => {
    if (isDestroyed || isRunning) return
    isRunning = true

    // 清理旧的依赖
    unsubscribers.forEach(unsub => unsub())
    unsubscribers.clear()

    const newDependencies = new Set<ReadonlySignal<any>>()
    const newUnsubscribers = new Set<Unsubscribe>()

    const prevComputation = currentComputation
    const mockComputation = {
      addDependency: (signal: ReadonlySignal<any>) => {
        if (!newDependencies.has(signal) && !isDestroyed) {
          newDependencies.add(signal)
          const unsubscribe = signal.subscribe(() => {
            if (!isDestroyed) {
              // 使用微任务避免同步递归调用
              Promise.resolve().then(() => {
                if (!isDestroyed) {
                  run()
                }
              })
            }
          })
          newUnsubscribers.add(unsubscribe)
        }
      }
    }
    currentComputation = mockComputation as any

    try {
      fn()
      // 更新依赖
      dependencies = newDependencies
      unsubscribers = newUnsubscribers
    } catch (error) {
      // 如果执行出错，清理新的订阅
      newUnsubscribers.forEach(unsub => unsub())
      throw error
    } finally {
      currentComputation = prevComputation
      isRunning = false
    }
  }

  run()
  return cleanup
}

export function when<T>(
  condition: () => boolean,
  trueFn: () => T,
  falseFn: () => T
): ComputedSignal<T> {
  return computed(() => condition() ? trueFn() : falseFn())
}

export function asyncSignal<T>(
  asyncFn: () => Promise<T>,
  initialValue: T
): WritableSignal<T> {
  const sig = signal(initialValue)

  asyncFn().then(value => sig.set(value))

  return sig
}