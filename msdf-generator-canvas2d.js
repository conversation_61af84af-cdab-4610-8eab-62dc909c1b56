// Canvas 2D MSDF纹理生成器
// 使用Canvas 2D API生成Multi-channel Signed Distance Field纹理
// 距离场计算函数
const calculateDistance = (x, y, imageData, width, height, maxDistance = 32) => {
    const data = imageData.data;
    const currentIndex = (y * width + x) * 4;
    const currentAlpha = data[currentIndex + 3];
    const isInside = currentAlpha > 128;
    let minDistance = maxDistance;
    // 搜索最近的边界
    for (let dy = -maxDistance; dy <= maxDistance; dy++) {
        for (let dx = -maxDistance; dx <= maxDistance; dx++) {
            const nx = x + dx;
            const ny = y + dy;
            if (nx < 0 || nx >= width || ny < 0 || ny >= height)
                continue;
            const neighborIndex = (ny * width + nx) * 4;
            const neighborAlpha = data[neighborIndex + 3];
            const neighborIsInside = neighborAlpha > 128;
            // 找到边界
            if (isInside !== neighborIsInside) {
                const distance = Math.sqrt(dx * dx + dy * dy);
                minDistance = Math.min(minDistance, distance);
            }
        }
    }
    return isInside ? minDistance : -minDistance;
};
// 生成单个字符的距离场
const generateCharacterSDF = (char, fontSize, padding, fontFamily = 'Arial') => {
    // 创建临时画布用于渲染字符
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    // 设置画布大小
    const size = fontSize + padding * 2;
    tempCanvas.width = size;
    tempCanvas.height = size;
    // 设置字体和样式
    tempCtx.font = `${fontSize}px ${fontFamily}`;
    tempCtx.textAlign = 'center';
    tempCtx.textBaseline = 'middle';
    tempCtx.fillStyle = 'white';
    // 测量字符
    const metrics = tempCtx.measureText(char);
    const actualWidth = metrics.width;
    const actualHeight = fontSize;
    // 清除并绘制字符
    tempCtx.clearRect(0, 0, size, size);
    tempCtx.fillText(char, size / 2, size / 2);
    // 获取像素数据
    const imageData = tempCtx.getImageData(0, 0, size, size);
    return {
        canvas: tempCanvas,
        metrics: {
            width: actualWidth,
            height: actualHeight,
            advance: actualWidth * 1.1,
            bounds: {
                left: (size - actualWidth) / 2,
                top: (size - actualHeight) / 2,
                right: (size + actualWidth) / 2,
                bottom: (size + actualHeight) / 2
            }
        }
    };
};
// 生成MSDF数据
const generateMSDF = (sourceCanvas, distanceRange = 4) => {
    const width = sourceCanvas.width;
    const height = sourceCanvas.height;
    // 获取源图像数据
    const sourceCtx = sourceCanvas.getContext('2d');
    const sourceImageData = sourceCtx.getImageData(0, 0, width, height);
    // 创建MSDF画布
    const msdfCanvas = document.createElement('canvas');
    msdfCanvas.width = width;
    msdfCanvas.height = height;
    const msdfCtx = msdfCanvas.getContext('2d');
    // 创建MSDF图像数据
    const msdfImageData = msdfCtx.createImageData(width, height);
    const msdfData = msdfImageData.data;
    // 计算每个像素的距离场
    for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
            const distance = calculateDistance(x, y, sourceImageData, width, height, distanceRange * 2);
            // 将距离转换为0-255范围
            const normalizedDistance = Math.max(0, Math.min(1, 0.5 + distance / (distanceRange * 2)));
            const value = Math.floor(normalizedDistance * 255);
            const index = (y * width + x) * 4;
            // MSDF使用RGB三个通道存储距离场信息
            // 这里简化为单通道，实际MSDF需要更复杂的算法
            msdfData[index] = value; // R
            msdfData[index + 1] = value; // G  
            msdfData[index + 2] = value; // B
            msdfData[index + 3] = 255; // A
        }
    }
    // 应用MSDF图像数据
    msdfCtx.putImageData(msdfImageData, 0, 0);
    return msdfCanvas;
};
// 改进的MSDF生成（多通道）
const generateMultiChannelMSDF = (sourceCanvas, distanceRange = 4) => {
    const width = sourceCanvas.width;
    const height = sourceCanvas.height;
    const sourceCtx = sourceCanvas.getContext('2d');
    const sourceImageData = sourceCtx.getImageData(0, 0, width, height);
    const msdfCanvas = document.createElement('canvas');
    msdfCanvas.width = width;
    msdfCanvas.height = height;
    const msdfCtx = msdfCanvas.getContext('2d');
    const msdfImageData = msdfCtx.createImageData(width, height);
    const msdfData = msdfImageData.data;
    // 为每个像素计算三个方向的距离场
    for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
            // 计算水平、垂直和对角线方向的距离
            const distanceH = calculateDistance(x, y, sourceImageData, width, height, distanceRange * 2);
            const distanceV = calculateDistance(x, y, sourceImageData, width, height, distanceRange * 2);
            const distanceD = calculateDistance(x, y, sourceImageData, width, height, distanceRange * 2);
            // 归一化到0-255
            const normalizeDistance = (d) => {
                return Math.floor(Math.max(0, Math.min(1, 0.5 + d / (distanceRange * 2))) * 255);
            };
            const index = (y * width + x) * 4;
            // 使用RGB三个通道存储不同方向的距离场
            msdfData[index] = normalizeDistance(distanceH); // R - 水平
            msdfData[index + 1] = normalizeDistance(distanceV); // G - 垂直
            msdfData[index + 2] = normalizeDistance(distanceD); // B - 对角线
            msdfData[index + 3] = 255; // A
        }
    }
    msdfCtx.putImageData(msdfImageData, 0, 0);
    return msdfCanvas;
};
// 生成字符图集
const generateMSDFAtlas = (characters, config) => {
    const { fontSize, padding, distanceRange, atlasWidth, atlasHeight, fontFamily } = config;
    // 创建图集画布
    const atlasCanvas = document.createElement('canvas');
    atlasCanvas.width = atlasWidth;
    atlasCanvas.height = atlasHeight;
    const atlasCtx = atlasCanvas.getContext('2d');
    // 清除画布
    atlasCtx.fillStyle = 'black';
    atlasCtx.fillRect(0, 0, atlasWidth, atlasHeight);
    const characterMap = new Map();
    // 计算网格布局
    const cellSize = fontSize + padding * 2;
    const cols = Math.floor(atlasWidth / cellSize);
    const rows = Math.floor(atlasHeight / cellSize);
    let currentX = 0;
    let currentY = 0;
    // 生成每个字符
    for (let i = 0; i < characters.length && i < cols * rows; i++) {
        const char = characters[i];
        // 生成字符的SDF
        const { canvas: charCanvas, metrics } = generateCharacterSDF(char, fontSize, padding, fontFamily);
        // 生成MSDF
        const msdfCanvas = generateMultiChannelMSDF(charCanvas, distanceRange);
        // 计算在图集中的位置
        const col = i % cols;
        const row = Math.floor(i / cols);
        const x = col * cellSize;
        const y = row * cellSize;
        // 绘制到图集
        atlasCtx.drawImage(msdfCanvas, x, y);
        // 保存字符信息
        const charInfo = {
            char: char,
            unicode: char.charCodeAt(0),
            x: x,
            y: y,
            width: cellSize,
            height: cellSize,
            xoffset: -padding,
            yoffset: -padding,
            xadvance: metrics.advance,
            bounds: {
                left: x,
                top: y,
                right: x + cellSize,
                bottom: y + cellSize
            }
        };
        characterMap.set(char, charInfo);
        currentX += cellSize;
        if (currentX + cellSize > atlasWidth) {
            currentX = 0;
            currentY += cellSize;
        }
    }
    // 计算字体度量
    const tempCtx = document.createElement('canvas').getContext('2d');
    tempCtx.font = `${fontSize}px ${fontFamily}`;
    const fontMetrics = tempCtx.measureText('Mg');
    const metrics = {
        lineHeight: fontSize * 1.2,
        baseline: fontSize * 0.8,
        ascender: fontSize * 0.8,
        descender: fontSize * 0.2
    };
    return {
        canvas: atlasCanvas,
        characters: characterMap,
        config: config,
        metrics: metrics
    };
};
// 导出为JSON格式
const exportMSDFData = (atlas) => {
    const chars = Array.from(atlas.characters.values()).map(char => ({
        id: char.unicode,
        char: char.char,
        x: char.x,
        y: char.y,
        width: char.width,
        height: char.height,
        xoffset: char.xoffset,
        yoffset: char.yoffset,
        xadvance: char.xadvance,
        page: 0
    }));
    const fontData = {
        pages: ["atlas.png"],
        chars: chars,
        info: {
            face: atlas.config.fontFamily,
            size: atlas.config.fontSize,
            charset: Array.from(atlas.characters.keys()),
            padding: [atlas.config.padding, atlas.config.padding, atlas.config.padding, atlas.config.padding]
        },
        common: {
            lineHeight: atlas.metrics.lineHeight,
            base: atlas.metrics.baseline,
            scaleW: atlas.config.atlasWidth,
            scaleH: atlas.config.atlasHeight,
            pages: 1
        },
        distanceField: {
            fieldType: "msdf",
            distanceRange: atlas.config.distanceRange
        }
    };
    return JSON.stringify(fontData, null, 2);
};
// 便捷函数：生成中文MSDF字体
const generateChineseMSDFFont = (characters = ['你', '好', '世', '界'], fontSize = 64, atlasSize = 512) => {
    const config = {
        fontSize: fontSize,
        padding: 8,
        distanceRange: 4,
        atlasWidth: atlasSize,
        atlasHeight: atlasSize,
        fontFamily: 'SimHei, Arial, sans-serif'
    };
    return generateMSDFAtlas(characters, config);
};
// 导出函数
export { generateMSDFAtlas, generateChineseMSDFFont, exportMSDFData, generateCharacterSDF, generateMSDF, generateMultiChannelMSDF };
