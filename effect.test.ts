import { signal, computed, effect } from './signal'

console.log('=== Effect 专项测试 ===')

// 测试1: 基础effect功能
console.log('\n--- 测试1: 基础effect ---')
const name = signal('Alice')
const age = signal(25)

let effectRunCount = 0
const cleanup1 = effect(() => {
  effectRunCount++
  console.log(`[${effectRunCount}] 用户信息: ${name.get()}, 年龄: ${age.get()}`)
})

console.log('初始effect运行次数:', effectRunCount) // 应该是1

name.set('Bob')
setTimeout(() => {
  console.log('name变化后effect运行次数:', effectRunCount) // 应该是2
  
  age.set(30)
  setTimeout(() => {
    console.log('age变化后effect运行次数:', effectRunCount) // 应该是3
    
    // 测试2: effect中使用计算Signal
    console.log('\n--- 测试2: effect + computed ---')
    const firstName = signal('张')
    const lastName = signal('三')
    const fullName = computed(() => `${firstName.get()}${lastName.get()}`)
    
    let computedEffectCount = 0
    const cleanup2 = effect(() => {
      computedEffectCount++
      console.log(`[${computedEffectCount}] 全名: ${fullName.get()}`)
    })
    
    setTimeout(() => {
      console.log('初始computed effect运行次数:', computedEffectCount) // 应该是1
      
      firstName.set('李')
      setTimeout(() => {
        console.log('firstName变化后运行次数:', computedEffectCount) // 应该是2
        
        lastName.set('四')
        setTimeout(() => {
          console.log('lastName变化后运行次数:', computedEffectCount) // 应该是3
          
          // 测试3: 清理effect
          console.log('\n--- 测试3: 清理effect ---')
          cleanup1()
          cleanup2()
          
          name.set('Charlie')
          age.set(35)
          firstName.set('王')
          
          setTimeout(() => {
            console.log('清理后effect运行次数应该不变:')
            console.log('- effectRunCount:', effectRunCount) // 应该还是3
            console.log('- computedEffectCount:', computedEffectCount) // 应该还是3
            
            // 测试4: effect中的条件逻辑
            console.log('\n--- 测试4: 条件effect ---')
            const isVisible = signal(true)
            const content = signal('内容A')
            
            let conditionalEffectCount = 0
            const cleanup3 = effect(() => {
              conditionalEffectCount++
              if (isVisible.get()) {
                console.log(`[${conditionalEffectCount}] 显示: ${content.get()}`)
              } else {
                console.log(`[${conditionalEffectCount}] 隐藏`)
              }
            })
            
            setTimeout(() => {
              content.set('内容B') // 应该触发effect
              setTimeout(() => {
                isVisible.set(false) // 应该触发effect
                setTimeout(() => {
                  content.set('内容C') // 仍然应该触发effect，因为content被访问了
                  setTimeout(() => {
                    console.log('条件effect运行次数:', conditionalEffectCount)
                    cleanup3()
                    console.log('\n=== Effect测试完成 ===')
                  }, 10)
                }, 10)
              }, 10)
            }, 10)
          }, 10)
        }, 10)
      }, 10)
    }, 10)
  }, 10)
}, 10)
