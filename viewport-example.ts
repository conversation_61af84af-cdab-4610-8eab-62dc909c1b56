import {
  ViewportInfo,
  ViewportConstraints,
  MatrixUtils,
  ViewportMatrixConstrainer,
  BounceAnimation,
  constrainViewportMatrix
} from './viewport-matrix-helper';

// 模拟Canvas视口管理器
export class CanvasViewportManager {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private currentMatrix: Matrix;
  private contentRect: Rect;
  private constrainer: ViewportMatrixConstrainer;
  private currentAnimation?: BounceAnimation;
  private animationId?: number;

  constructor(canvas: HTMLCanvasElement, contentRect: Rect) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d')!;
    this.currentMatrix = MatrixUtils.identity();
    this.contentRect = contentRect;
    
    // 创建默认约束
    const constraints: ViewportConstraints = {
      minScale: 0.1,
      maxScale: 5,
      keepVisibleProportion: 0.2 // 保持20%内容可见
    };
    
    const viewport: ViewportInfo = {
      width: canvas.width,
      height: canvas.height,
      matrix: this.currentMatrix,
      contentRect: this.contentRect
    };
    
    this.constrainer = new ViewportMatrixConstrainer(viewport, constraints);
    
    // 绑定事件
    this.bindEvents();
  }

  private bindEvents(): void {
    // 鼠标滚轮缩放
    this.canvas.addEventListener('wheel', (e) => {
      e.preventDefault();
      const rect = this.canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
      this.zoomAt(x, y, scaleFactor);
    });

    // 鼠标拖拽平移
    let isDragging = false;
    let lastX = 0;
    let lastY = 0;

    this.canvas.addEventListener('mousedown', (e) => {
      isDragging = true;
      lastX = e.clientX;
      lastY = e.clientY;
    });

    this.canvas.addEventListener('mousemove', (e) => {
      if (isDragging) {
        const deltaX = e.clientX - lastX;
        const deltaY = e.clientY - lastY;
        this.pan(deltaX, deltaY);
        lastX = e.clientX;
        lastY = e.clientY;
      }
    });

    this.canvas.addEventListener('mouseup', () => {
      isDragging = false;
    });

    // 窗口大小变化
    window.addEventListener('resize', () => {
      this.updateCanvasSize();
    });
  }

  // 在指定点缩放
  private zoomAt(x: number, y: number, scaleFactor: number): void {
    // 将屏幕坐标转换为世界坐标
    const invMatrix = MatrixUtils.invert(this.currentMatrix);
    if (!invMatrix) return;
    
    const worldPoint = MatrixUtils.transformPoint({ x, y }, invMatrix);
    
    // 应用缩放
    const scaleMatrix = MatrixUtils.scale(scaleFactor, scaleFactor);
    const newMatrix = MatrixUtils.multiply(this.currentMatrix, scaleMatrix);
    
    // 调整平移以保持缩放中心点不变
    const newWorldPoint = MatrixUtils.transformPoint({ x, y }, MatrixUtils.invert(newMatrix)!);
    const deltaX = (worldPoint.x - newWorldPoint.x) * MatrixUtils.getScale(newMatrix).x;
    const deltaY = (worldPoint.y - newWorldPoint.y) * MatrixUtils.getScale(newMatrix).y;
    
    newMatrix[4] += deltaX;
    newMatrix[5] += deltaY;
    
    this.setMatrix(newMatrix);
  }

  // 平移
  private pan(deltaX: number, deltaY: number): void {
    const newMatrix = [...this.currentMatrix] as Matrix;
    newMatrix[4] += deltaX;
    newMatrix[5] += deltaY;
    this.setMatrix(newMatrix);
  }

  // 设置矩阵并应用约束
  private setMatrix(matrix: Matrix): void {
    const viewport: ViewportInfo = {
      width: this.canvas.width,
      height: this.canvas.height,
      matrix,
      contentRect: this.contentRect
    };

    this.constrainer.updateViewport(viewport);
    const result = this.constrainer.constrainMatrix(matrix);

    if (result.needsAnimation && result.animation) {
      this.startAnimation(result.animation as BounceAnimation);
    } else {
      this.currentMatrix = result.constrainedMatrix;
      this.render();
    }
  }

  // 启动动画
  private startAnimation(animation: BounceAnimation): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    
    this.currentAnimation = animation;
    
    const animate = () => {
      if (!this.currentAnimation) return;
      
      const shouldContinue = this.currentAnimation.tick(16); // 60fps
      this.currentMatrix = this.currentAnimation.getCurrentMatrix();
      this.render();
      
      if (shouldContinue) {
        this.animationId = requestAnimationFrame(animate);
      } else {
        this.currentAnimation = undefined;
        this.animationId = undefined;
      }
    };
    
    animate();
  }

  // 更新画布大小
  private updateCanvasSize(): void {
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
    
    const viewport: ViewportInfo = {
      width: this.canvas.width,
      height: this.canvas.height,
      matrix: this.currentMatrix,
      contentRect: this.contentRect
    };
    
    this.constrainer.updateViewport(viewport);
    this.render();
  }

  // 渲染
  private render(): void {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // 应用变换矩阵
    this.ctx.save();
    const [a, b, c, d, tx, ty] = this.currentMatrix;
    this.ctx.setTransform(a, b, c, d, tx, ty);
    
    // 绘制内容
    this.drawContent();
    
    this.ctx.restore();
    
    // 绘制调试信息
    this.drawDebugInfo();
  }

  // 绘制内容
  private drawContent(): void {
    // 绘制内容区域边框
    this.ctx.strokeStyle = '#333';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(
      this.contentRect.x,
      this.contentRect.y,
      this.contentRect.width,
      this.contentRect.height
    );
    
    // 绘制网格
    this.ctx.strokeStyle = '#ddd';
    this.ctx.lineWidth = 1;
    
    const gridSize = 50;
    for (let x = 0; x <= this.contentRect.width; x += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, 0);
      this.ctx.lineTo(x, this.contentRect.height);
      this.ctx.stroke();
    }
    
    for (let y = 0; y <= this.contentRect.height; y += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(0, y);
      this.ctx.lineTo(this.contentRect.width, y);
      this.ctx.stroke();
    }
    
    // 绘制一些示例对象
    this.ctx.fillStyle = '#ff6b6b';
    this.ctx.fillRect(100, 100, 100, 100);
    
    this.ctx.fillStyle = '#4ecdc4';
    this.ctx.fillRect(300, 200, 150, 80);
    
    this.ctx.fillStyle = '#45b7d1';
    this.ctx.fillRect(500, 150, 80, 120);
  }

  // 绘制调试信息
  private drawDebugInfo(): void {
    this.ctx.fillStyle = '#000';
    this.ctx.font = '14px monospace';
    
    const scale = MatrixUtils.getScale(this.currentMatrix);
    const translation = MatrixUtils.getTranslation(this.currentMatrix);
    
    this.ctx.fillText(`缩放: ${scale.x.toFixed(2)}x, ${scale.y.toFixed(2)}x`, 10, 20);
    this.ctx.fillText(`平移: ${translation.x.toFixed(0)}, ${translation.y.toFixed(0)}`, 10, 40);
    this.ctx.fillText(`动画: ${this.currentAnimation ? '进行中' : '无'}`, 10, 60);
  }

  // 重置视图
  resetView(): void {
    this.setMatrix(MatrixUtils.identity());
  }

  // 适应内容
  fitContent(): void {
    const scaleX = this.canvas.width / this.contentRect.width;
    const scaleY = this.canvas.height / this.contentRect.height;
    const scale = Math.min(scaleX, scaleY) * 0.9; // 留10%边距
    
    const centerX = this.canvas.width / 2;
    const centerY = this.canvas.height / 2;
    const contentCenterX = this.contentRect.width / 2;
    const contentCenterY = this.contentRect.height / 2;
    
    const matrix = MatrixUtils.multiply(
      MatrixUtils.translate(centerX - contentCenterX * scale, centerY - contentCenterY * scale),
      MatrixUtils.scale(scale, scale)
    );
    
    this.setMatrix(matrix);
  }
}

// 使用示例
/*
// HTML中创建canvas
const canvas = document.createElement('canvas');
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;
document.body.appendChild(canvas);

// 定义内容区域
const contentRect = { x: 0, y: 0, width: 1000, height: 800 };

// 创建视口管理器
const viewportManager = new CanvasViewportManager(canvas, contentRect);

// 添加控制按钮
const resetBtn = document.createElement('button');
resetBtn.textContent = '重置视图';
resetBtn.onclick = () => viewportManager.resetView();
document.body.appendChild(resetBtn);

const fitBtn = document.createElement('button');
fitBtn.textContent = '适应内容';
fitBtn.onclick = () => viewportManager.fitContent();
document.body.appendChild(fitBtn);
*/
