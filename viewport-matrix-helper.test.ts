import {
  ViewportInfo,
  ViewportConstraints,
  MatrixUtils,
  GeometryUtils,
  ViewportMatrixConstrainer,
  BounceAnimation,
  constrainViewportMatrix,
  createDefaultConstraints,
  needsConstraining
} from './viewport-matrix-helper';

// 测试矩阵工具函数
console.log('=== 矩阵工具函数测试 ===');

// 测试单位矩阵
const identity = MatrixUtils.identity();
console.log('单位矩阵:', identity); // [1, 0, 0, 1, 0, 0]

// 测试矩阵乘法
const scale2x = MatrixUtils.scale(2, 2);
const translate100 = MatrixUtils.translate(100, 50);
const combined = MatrixUtils.multiply(scale2x, translate100);
console.log('缩放2倍后平移(100,50):', combined);

// 测试点变换
const point = { x: 10, y: 20 };
const transformedPoint = MatrixUtils.transformPoint(point, combined);
console.log('点(10,20)变换后:', transformedPoint);

// 测试矩形变换
const rect = { x: 0, y: 0, width: 100, height: 50 };
const transformedRect = MatrixUtils.transformRect(rect, combined);
console.log('矩形变换后:', transformedRect);

// 测试获取矩阵属性
const scaleValue = MatrixUtils.getScale(combined);
const translation = MatrixUtils.getTranslation(combined);
const rotation = MatrixUtils.getRotation(combined);
console.log('缩放值:', scaleValue);
console.log('平移值:', translation);
console.log('旋转角度:', rotation);

console.log('\n=== 几何工具函数测试 ===');

// 测试矩形交集
const rect1 = { x: 0, y: 0, width: 100, height: 100 };
const rect2 = { x: 50, y: 50, width: 100, height: 100 };
const intersection = GeometryUtils.intersectRect(rect1, rect2);
console.log('矩形交集:', intersection);

// 测试重叠比例
const overlapRatio = GeometryUtils.getOverlapRatio(rect1, rect2);
console.log('重叠比例:', overlapRatio);

// 测试包含关系
const container = { x: 0, y: 0, width: 200, height: 200 };
const contained = { x: 50, y: 50, width: 50, height: 50 };
const contains = GeometryUtils.containsRect(container, contained);
console.log('包含关系:', contains);

console.log('\n=== 视口矩阵约束测试 ===');

// 创建测试视口
const viewport: ViewportInfo = {
  width: 800,
  height: 600,
  matrix: [2, 0, 0, 2, -200, -150], // 放大2倍，偏移(-200, -150)
  contentRect: { x: 0, y: 0, width: 1000, height: 800 }
};

// 创建约束
const constraints: ViewportConstraints = {
  minScale: 0.5,
  maxScale: 4,
  keepVisibleProportion: 0.3 // 保持30%内容可见
};

// 测试约束
const result = constrainViewportMatrix(viewport, constraints);
console.log('需要动画:', result.needsAnimation);
console.log('约束后矩阵:', result.constrainedMatrix);

// 测试默认约束
const defaultConstraints = createDefaultConstraints();
console.log('默认约束:', defaultConstraints);

// 测试是否需要约束
const needsAnim = needsConstraining(viewport, constraints);
console.log('是否需要约束:', needsAnim);

console.log('\n=== 动画测试 ===');

// 测试反弹动画
const fromMatrix = viewport.matrix;
const toMatrix = result.constrainedMatrix;
const animation = new BounceAnimation(fromMatrix, toMatrix, 1000);

// 模拟动画帧
let frame = 0;
const maxFrames = 10;
const frameInterval = 100; // 100ms per frame

const animateTest = () => {
  if (frame < maxFrames) {
    const shouldContinue = animation.tick(frameInterval);
    const currentMatrix = animation.getCurrentMatrix();
    console.log(`帧 ${frame}: 继续=${shouldContinue}, 矩阵=${currentMatrix.map(v => v.toFixed(2))}`);
    frame++;
    if (shouldContinue) {
      setTimeout(animateTest, 10);
    } else {
      console.log('动画结束');
    }
  }
};

animateTest();

console.log('\n=== 边界情况测试 ===');

// 测试无限制约束
const noLimitConstraints: ViewportConstraints = {
  minScale: 0.1,
  maxScale: 10,
  keepVisibleProportion: -1 // 无限制
};

const noLimitResult = constrainViewportMatrix(viewport, noLimitConstraints);
console.log('无限制约束结果:', noLimitResult.needsAnimation);

// 测试完全可见约束
const fullVisibleConstraints: ViewportConstraints = {
  minScale: 0.1,
  maxScale: 10,
  keepVisibleProportion: 1 // 完全可见
};

const fullVisibleResult = constrainViewportMatrix(viewport, fullVisibleConstraints);
console.log('完全可见约束结果:', fullVisibleResult.needsAnimation);

// 测试矩阵求逆
const invertible = [2, 0, 0, 2, 100, 50] as const;
const inverted = MatrixUtils.invert(invertible);
console.log('矩阵求逆:', inverted);

// 测试不可逆矩阵
const singular = [1, 0, 2, 0, 0, 0] as const;
const singularInverted = MatrixUtils.invert(singular);
console.log('不可逆矩阵求逆:', singularInverted);

console.log('\n🎉 视口矩阵限制算法测试完成');
