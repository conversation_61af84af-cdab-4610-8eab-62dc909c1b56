<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试生成的MSDF数据</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #4CAF50;
        }
        
        .section {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .canvas-wrapper {
            text-align: center;
        }
        
        .canvas-wrapper h3 {
            margin: 10px 0;
            color: #ccc;
        }
        
        canvas {
            border: 1px solid #333;
            background: #000;
            border-radius: 4px;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            text-align: center;
            padding: 10px;
            background: #444;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试生成的MSDF数据</h1>
        
        <div class="section">
            <h2>生成和测试流程</h2>
            <div class="controls">
                <button onclick="generateAndTest()">生成MSDF并测试</button>
                <button onclick="testChinese()">测试中文字符</button>
                <button onclick="testEnglish()">测试英文字符</button>
                <button onclick="clearAll()">清除所有</button>
            </div>
            <div class="status" id="status">点击按钮开始测试</div>
        </div>
        
        <div class="section">
            <h2>生成的MSDF图集</h2>
            <div class="comparison">
                <div class="canvas-wrapper">
                    <h3>原始图集</h3>
                    <canvas id="atlasCanvas" width="300" height="300"></canvas>
                </div>
                <div class="canvas-wrapper">
                    <h3>放大预览</h3>
                    <canvas id="zoomedCanvas" width="300" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>渲染测试</h2>
            <div class="comparison">
                <div class="canvas-wrapper">
                    <h3>Canvas 2D渲染</h3>
                    <canvas id="canvas2dRender" width="400" height="200"></canvas>
                </div>
                <div class="canvas-wrapper">
                    <h3>WebGL模拟渲染</h3>
                    <canvas id="webglRender" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="info" id="generatedInfo">
            等待生成MSDF数据...
        </div>
    </div>

    <script type="module">
        import { 
            generateMSDFAtlas, 
            generateChineseMSDFFont, 
            exportMSDFData 
        } from './msdf-generator-canvas2d.js';
        import { drawInCanvas2DByGlData } from './canvas2d-gl-renderer.js';
        
        // 获取画布
        const atlasCanvas = document.getElementById('atlasCanvas');
        const zoomedCanvas = document.getElementById('zoomedCanvas');
        const canvas2dRender = document.getElementById('canvas2dRender');
        const webglRender = document.getElementById('webglRender');
        
        const atlasCtx = atlasCanvas.getContext('2d');
        const zoomedCtx = zoomedCanvas.getContext('2d');
        const canvas2dCtx = canvas2dRender.getContext('2d');
        const webglCtx = webglRender.getContext('2d');
        
        let currentAtlas = null;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log(message);
        }
        
        function updateInfo(atlas) {
            const jsonData = exportMSDFData(atlas);
            const info = `
生成的MSDF数据:
字符数量: ${atlas.characters.size}
图集尺寸: ${atlas.config.atlasWidth}x${atlas.config.atlasHeight}
字体大小: ${atlas.config.fontSize}px
距离范围: ${atlas.config.distanceRange}

支持的字符: ${Array.from(atlas.characters.keys()).join(' ')}

JSON数据大小: ${(jsonData.length / 1024).toFixed(1)} KB
            `.trim();
            
            document.getElementById('generatedInfo').textContent = info;
        }
        
        function clearAll() {
            atlasCtx.clearRect(0, 0, atlasCanvas.width, atlasCanvas.height);
            zoomedCtx.clearRect(0, 0, zoomedCanvas.width, zoomedCanvas.height);
            canvas2dCtx.clearRect(0, 0, canvas2dRender.width, canvas2dRender.height);
            webglCtx.clearRect(0, 0, webglRender.width, webglRender.height);
            document.getElementById('generatedInfo').textContent = '等待生成MSDF数据...';
            updateStatus('已清除所有内容');
        }
        
        function generateAndTest() {
            try {
                updateStatus('正在生成MSDF图集...');
                
                // 生成中文MSDF字体
                currentAtlas = generateChineseMSDFFont(['你', '好', '世', '界', 'M', 'S', 'D', 'F'], 64, 512);
                
                // 显示图集
                showAtlas(currentAtlas);
                
                // 显示放大预览
                showZoomedPreview(currentAtlas);
                
                // 测试渲染
                testRendering(currentAtlas);
                
                // 更新信息
                updateInfo(currentAtlas);
                
                updateStatus('MSDF生成和测试完成!');
                
            } catch (error) {
                updateStatus('错误: ' + error.message);
                console.error(error);
            }
        }
        
        function testChinese() {
            try {
                updateStatus('生成中文MSDF字体...');
                
                const chineseChars = ['你', '好', '世', '界', '中', '文', '字', '体', '测', '试'];
                const config = {
                    fontSize: 48,
                    padding: 6,
                    distanceRange: 4,
                    atlasWidth: 512,
                    atlasHeight: 512,
                    fontFamily: 'SimHei, Arial, sans-serif'
                };
                
                currentAtlas = generateMSDFAtlas(chineseChars, config);
                
                showAtlas(currentAtlas);
                showZoomedPreview(currentAtlas);
                testRendering(currentAtlas);
                updateInfo(currentAtlas);
                
                updateStatus('中文MSDF字体生成完成!');
                
            } catch (error) {
                updateStatus('错误: ' + error.message);
                console.error(error);
            }
        }
        
        function testEnglish() {
            try {
                updateStatus('生成英文MSDF字体...');
                
                const englishChars = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'M', 'S', 'D', 'F'];
                const config = {
                    fontSize: 48,
                    padding: 6,
                    distanceRange: 4,
                    atlasWidth: 512,
                    atlasHeight: 512,
                    fontFamily: 'Arial, sans-serif'
                };
                
                currentAtlas = generateMSDFAtlas(englishChars, config);
                
                showAtlas(currentAtlas);
                showZoomedPreview(currentAtlas);
                testRendering(currentAtlas);
                updateInfo(currentAtlas);
                
                updateStatus('英文MSDF字体生成完成!');
                
            } catch (error) {
                updateStatus('错误: ' + error.message);
                console.error(error);
            }
        }
        
        function showAtlas(atlas) {
            atlasCtx.clearRect(0, 0, atlasCanvas.width, atlasCanvas.height);
            
            // 缩放图集以适应画布
            const scale = Math.min(
                atlasCanvas.width / atlas.canvas.width,
                atlasCanvas.height / atlas.canvas.height
            );
            
            const scaledWidth = atlas.canvas.width * scale;
            const scaledHeight = atlas.canvas.height * scale;
            const offsetX = (atlasCanvas.width - scaledWidth) / 2;
            const offsetY = (atlasCanvas.height - scaledHeight) / 2;
            
            atlasCtx.drawImage(atlas.canvas, offsetX, offsetY, scaledWidth, scaledHeight);
        }
        
        function showZoomedPreview(atlas) {
            zoomedCtx.clearRect(0, 0, zoomedCanvas.width, zoomedCanvas.height);
            
            // 显示第一个字符的放大版本
            const firstChar = Array.from(atlas.characters.values())[0];
            if (firstChar) {
                const cellSize = atlas.config.fontSize + atlas.config.padding * 2;
                
                // 从图集中提取第一个字符
                zoomedCtx.drawImage(
                    atlas.canvas,
                    firstChar.x, firstChar.y, cellSize, cellSize,
                    0, 0, zoomedCanvas.width, zoomedCanvas.height
                );
                
                // 添加网格线
                zoomedCtx.strokeStyle = '#ff0000';
                zoomedCtx.lineWidth = 1;
                zoomedCtx.strokeRect(0, 0, zoomedCanvas.width, zoomedCanvas.height);
                
                // 添加标签
                zoomedCtx.fillStyle = '#ffff00';
                zoomedCtx.font = '16px Arial';
                zoomedCtx.fillText(`字符: ${firstChar.char}`, 10, 25);
            }
        }
        
        function testRendering(atlas) {
            // 清除画布
            canvas2dCtx.clearRect(0, 0, canvas2dRender.width, canvas2dRender.height);
            webglCtx.clearRect(0, 0, webglRender.width, webglRender.height);
            
            // Canvas 2D渲染测试
            canvas2dCtx.fillStyle = '#333';
            canvas2dCtx.fillRect(0, 0, canvas2dRender.width, canvas2dRender.height);
            
            // 生成简单的几何数据来测试渲染
            const testChars = Array.from(atlas.characters.keys()).slice(0, 4);
            let x = 20;
            
            testChars.forEach((char, index) => {
                const charInfo = atlas.characters.get(char);
                if (charInfo) {
                    // 创建简单的四边形几何
                    const size = 40;
                    const vertices = new Float32Array([
                        x, 50, 0, 0,
                        x + size, 50, 1, 0,
                        x + size, 50 + size, 1, 1,
                        x, 50 + size, 0, 1
                    ]);
                    
                    const indices = new Uint16Array([0, 1, 2, 0, 2, 3]);
                    
                    // 使用Canvas 2D渲染器绘制
                    drawInCanvas2DByGlData(canvas2dCtx, vertices, indices, {
                        fillStyle: `hsl(${index * 60}, 70%, 60%)`,
                        strokeStyle: '#fff',
                        lineWidth: 2
                    });
                    
                    // 添加字符标签
                    canvas2dCtx.fillStyle = '#fff';
                    canvas2dCtx.font = '14px Arial';
                    canvas2dCtx.fillText(char, x + 5, 45);
                    
                    x += size + 20;
                }
            });
            
            // WebGL模拟渲染
            webglCtx.fillStyle = '#333';
            webglCtx.fillRect(0, 0, webglRender.width, webglRender.height);
            
            webglCtx.fillStyle = '#4CAF50';
            webglCtx.font = '32px Arial';
            webglCtx.fillText('MSDF渲染测试', 50, 100);
            
            webglCtx.fillStyle = '#ff6b6b';
            webglCtx.font = '24px Arial';
            const testText = testChars.join('');
            webglCtx.fillText(testText, 50, 150);
        }
        
        // 全局函数
        window.generateAndTest = generateAndTest;
        window.testChinese = testChinese;
        window.testEnglish = testEnglish;
        window.clearAll = clearAll;
        
        // 初始化
        updateStatus('MSDF测试器已就绪');
    </script>
</body>
</html>
