// MSDF文本渲染 - 函数式实现
// 基于 gl/res/custom-msdf.json 字体数据

// 类型定义
interface MSDFChar {
  id: number;
  char: string;
  width: number;
  height: number;
  xoffset: number;
  yoffset: number;
  xadvance: number;
  x: number;
  y: number;
  page: number;
}

interface MSDFFontData {
  chars: MSDFChar[];
  info: {
    size: number;
    charset: string[];
  };
  common: {
    lineHeight: number;
    base: number;
    scaleW: number;
    scaleH: number;
  };
  distanceField: {
    fieldType: string;
    distanceRange: number;
  };
}

interface RenderContext {
  gl: WebGL2RenderingContext;
  program: WebGLProgram;
  vao: WebGLVertexArrayObject;
  vertexBuffer: WebGLBuffer;
  indexBuffer: WebGLBuffer;
  texture: WebGLTexture;
  uniforms: Record<string, WebGLUniformLocation>;
}

// 着色器源码
const vertexShaderSource = `#version 300 es
precision highp float;

in vec2 a_position;
in vec2 a_texCoord;

uniform mat4 u_projection;
uniform vec2 u_translation;
uniform float u_scale;

out vec2 v_texCoord;

void main() {
    vec2 scaledPos = a_position * u_scale;
    vec2 worldPos = scaledPos + u_translation;
    gl_Position = u_projection * vec4(worldPos, 0.0, 1.0);
    v_texCoord = a_texCoord;
}
`;

const fragmentShaderSource = `#version 300 es
precision highp float;

in vec2 v_texCoord;

uniform sampler2D u_msdfTexture;
uniform vec3 u_color;
uniform vec3 u_outlineColor;
uniform float u_pxRange;
uniform float u_outlineWidth;
uniform float u_smoothness;

out vec4 fragColor;

float median(float r, float g, float b) {
    return max(min(r, g), min(max(r, g), b));
}

float screenPxRange() {
    vec2 unitRange = vec2(u_pxRange) / vec2(textureSize(u_msdfTexture, 0));
    vec2 screenTexSize = vec2(1.0) / fwidth(v_texCoord);
    return max(0.5 * dot(unitRange, screenTexSize), 1.0);
}

void main() {
    vec3 msd = texture(u_msdfTexture, v_texCoord).rgb;
    float sd = median(msd.r, msd.g, msd.b);
    float screenPxDistance = screenPxRange() * (sd - 0.5);
    
    // 主文本
    float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
    alpha = smoothstep(0.5 - u_smoothness, 0.5 + u_smoothness, sd);
    
    vec3 finalColor = u_color;
    float finalAlpha = alpha;
    
    // 描边效果
    if (u_outlineWidth > 0.0) {
        float outlineAlpha = smoothstep(0.5 - u_outlineWidth - u_smoothness, 
                                      0.5 - u_outlineWidth + u_smoothness, sd);
        finalColor = mix(u_outlineColor, u_color, alpha);
        finalAlpha = max(outlineAlpha, alpha);
    }
    
    fragColor = vec4(finalColor, finalAlpha);
}
`;

// 工具函数
const createShader = (gl: WebGL2RenderingContext, type: number, source: string): WebGLShader => {
  const shader = gl.createShader(type)!;
  gl.shaderSource(shader, source);
  gl.compileShader(shader);
  
  if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
    const error = gl.getShaderInfoLog(shader);
    gl.deleteShader(shader);
    throw new Error(`Shader compile error: ${error}`);
  }
  
  return shader;
};

const createProgram = (gl: WebGL2RenderingContext, vertexSource: string, fragmentSource: string): WebGLProgram => {
  const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexSource);
  const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentSource);
  
  const program = gl.createProgram()!;
  gl.attachShader(program, vertexShader);
  gl.attachShader(program, fragmentShader);
  gl.linkProgram(program);
  
  if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
    const error = gl.getProgramInfoLog(program);
    gl.deleteProgram(program);
    throw new Error(`Program link error: ${error}`);
  }
  
  gl.deleteShader(vertexShader);
  gl.deleteShader(fragmentShader);
  
  return program;
};

const createTexture = (gl: WebGL2RenderingContext, image: HTMLImageElement): WebGLTexture => {
  const texture = gl.createTexture()!;
  gl.bindTexture(gl.TEXTURE_2D, texture);
  gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGB, gl.RGB, gl.UNSIGNED_BYTE, image);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
  return texture;
};

const createOrthographicMatrix = (width: number, height: number): Float32Array => {
  return new Float32Array([
    2 / width, 0, 0, 0,
    0, -2 / height, 0, 0,
    0, 0, -1, 0,
    -1, 1, 0, 1
  ]);
};

// 初始化渲染上下文
const initRenderContext = async (canvas: HTMLCanvasElement): Promise<RenderContext> => {
  const gl = canvas.getContext('webgl2')!;
  if (!gl) {
    throw new Error('WebGL2 not supported');
  }
  
  // 创建着色器程序
  const program = createProgram(gl, vertexShaderSource, fragmentShaderSource);
  
  // 获取uniform位置
  const uniforms = {
    projection: gl.getUniformLocation(program, 'u_projection')!,
    translation: gl.getUniformLocation(program, 'u_translation')!,
    scale: gl.getUniformLocation(program, 'u_scale')!,
    msdfTexture: gl.getUniformLocation(program, 'u_msdfTexture')!,
    color: gl.getUniformLocation(program, 'u_color')!,
    outlineColor: gl.getUniformLocation(program, 'u_outlineColor')!,
    pxRange: gl.getUniformLocation(program, 'u_pxRange')!,
    outlineWidth: gl.getUniformLocation(program, 'u_outlineWidth')!,
    smoothness: gl.getUniformLocation(program, 'u_smoothness')!
  };
  
  // 创建VAO
  const vao = gl.createVertexArray()!;
  gl.bindVertexArray(vao);
  
  // 创建缓冲区
  const vertexBuffer = gl.createBuffer()!;
  const indexBuffer = gl.createBuffer()!;
  
  // 设置顶点属性
  const positionLocation = gl.getAttribLocation(program, 'a_position');
  const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');
  
  gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
  gl.enableVertexAttribArray(positionLocation);
  gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);
  gl.enableVertexAttribArray(texCoordLocation);
  gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);
  
  gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
  
  // 加载MSDF纹理
  const image = new Image();
  image.src = 'gl/res/custom.png'; // 对应JSON中的pages[0]
  await new Promise((resolve, reject) => {
    image.onload = resolve;
    image.onerror = reject;
  });
  
  const texture = createTexture(gl, image);
  
  // 启用混合
  gl.enable(gl.BLEND);
  gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
  
  return {
    gl,
    program,
    vao,
    vertexBuffer,
    indexBuffer,
    texture,
    uniforms
  };
};

// 加载字体数据
const loadFontData = async (): Promise<MSDFFontData> => {
  const response = await fetch('gl/res/custom-msdf.json');
  return await response.json();
};

// 生成文本几何数据
const generateTextGeometry = (text: string, fontData: MSDFFontData, fontSize: number) => {
  const vertices: number[] = [];
  const indices: number[] = [];
  const scale = fontSize / fontData.info.size;
  
  let x = 0;
  let vertexIndex = 0;
  
  // 创建字符映射
  const charMap = new Map<string, MSDFChar>();
  fontData.chars.forEach(char => charMap.set(char.char, char));
  
  for (const char of text) {
    const charData = charMap.get(char);
    if (!charData) {
      x += fontSize * 0.5; // 空格
      continue;
    }
    
    // 计算字符位置和大小
    const charX = x + charData.xoffset * scale;
    const charY = charData.yoffset * scale;
    const charWidth = charData.width * scale;
    const charHeight = charData.height * scale;
    
    // 计算纹理坐标
    const u1 = charData.x / fontData.common.scaleW;
    const v1 = charData.y / fontData.common.scaleH;
    const u2 = (charData.x + charData.width) / fontData.common.scaleW;
    const v2 = (charData.y + charData.height) / fontData.common.scaleH;
    
    // 添加四个顶点 (x, y, u, v)
    vertices.push(
      charX, charY, u1, v1,                    // 左下
      charX + charWidth, charY, u2, v1,       // 右下
      charX + charWidth, charY + charHeight, u2, v2, // 右上
      charX, charY + charHeight, u1, v2       // 左上
    );
    
    // 添加两个三角形的索引
    indices.push(
      vertexIndex, vertexIndex + 1, vertexIndex + 2,
      vertexIndex, vertexIndex + 2, vertexIndex + 3
    );
    
    vertexIndex += 4;
    x += charData.xadvance * scale;
  }
  
  return {
    vertices: new Float32Array(vertices),
    indices: new Uint16Array(indices),
    width: x
  };
};

// 渲染文本
const renderText = (
  context: RenderContext,
  fontData: MSDFFontData,
  text: string,
  x: number,
  y: number,
  fontSize: number,
  color: [number, number, number] = [1, 1, 1],
  outlineColor: [number, number, number] = [0, 0, 0],
  outlineWidth: number = 0,
  smoothness: number = 0.05
) => {
  const { gl, program, vao, vertexBuffer, indexBuffer, texture, uniforms } = context;
  
  // 生成几何数据
  const geometry = generateTextGeometry(text, fontData, fontSize);
  
  // 清除画布
  gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);
  gl.clearColor(0.1, 0.1, 0.1, 1);
  gl.clear(gl.COLOR_BUFFER_BIT);
  
  // 使用着色器程序
  gl.useProgram(program);
  gl.bindVertexArray(vao);
  
  // 更新缓冲区数据
  gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
  gl.bufferData(gl.ARRAY_BUFFER, geometry.vertices, gl.DYNAMIC_DRAW);
  
  gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
  gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, geometry.indices, gl.DYNAMIC_DRAW);
  
  // 设置uniforms
  const projectionMatrix = createOrthographicMatrix(gl.canvas.width, gl.canvas.height);
  gl.uniformMatrix4fv(uniforms.projection, false, projectionMatrix);
  gl.uniform2f(uniforms.translation, x, y);
  gl.uniform1f(uniforms.scale, 1.0);
  gl.uniform3fv(uniforms.color, color);
  gl.uniform3fv(uniforms.outlineColor, outlineColor);
  gl.uniform1f(uniforms.pxRange, fontData.distanceField.distanceRange);
  gl.uniform1f(uniforms.outlineWidth, outlineWidth);
  gl.uniform1f(uniforms.smoothness, smoothness);
  
  // 绑定纹理
  gl.activeTexture(gl.TEXTURE0);
  gl.bindTexture(gl.TEXTURE_2D, texture);
  gl.uniform1i(uniforms.msdfTexture, 0);
  
  // 绘制
  gl.drawElements(gl.TRIANGLES, geometry.indices.length, gl.UNSIGNED_SHORT, 0);
};

// 主函数 - 演示示例
const main = async () => {
  try {
    // 获取canvas
    const canvas = document.getElementById('canvas') as HTMLCanvasElement;
    if (!canvas) {
      throw new Error('Canvas not found');
    }
    
    // 初始化渲染上下文
    const context = await initRenderContext(canvas);
    
    // 加载字体数据
    const fontData = await loadFontData();
    
    console.log('字体数据加载完成:', fontData);
    console.log('支持的字符:', fontData.info.charset);
    
    // 渲染函数
    const render = (
      text: string = '你好世界',
      fontSize: number = 48,
      x: number = 50,
      y: number = 100,
      color: [number, number, number] = [1, 1, 1],
      outlineWidth: number = 0.05,
      smoothness: number = 0.05
    ) => {
      renderText(
        context,
        fontData,
        text,
        x,
        y,
        fontSize,
        color,
        [0, 0, 0], // 描边颜色
        outlineWidth,
        smoothness
      );
    };
    
    // 初始渲染
    render();
    
    // 导出到全局，方便调试
    (window as any).render = render;
    (window as any).fontData = fontData;
    
    console.log('MSDF渲染器初始化完成');
    console.log('使用 render(text, fontSize, x, y, color, outlineWidth, smoothness) 来渲染文本');
    
  } catch (error) {
    console.error('初始化失败:', error);
  }
};

// 启动应用
if (typeof window !== 'undefined') {
  window.addEventListener('DOMContentLoaded', main);
}

export {
  initRenderContext,
  loadFontData,
  generateTextGeometry,
  renderText,
  main
};
