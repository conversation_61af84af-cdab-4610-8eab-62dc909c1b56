// WebGL2 MSDF顶点着色器
const vertexShaderSource = `#version 300 es
precision highp float;

in vec2 a_position;
in vec2 a_texCoord;

uniform mat4 u_projection;
uniform vec2 u_translation;
uniform float u_scale;

out vec2 v_texCoord;

void main() {
    vec2 scaledPos = a_position * u_scale;
    vec2 worldPos = scaledPos + u_translation;
    gl_Position = u_projection * vec4(worldPos, 0.0, 1.0);
    v_texCoord = a_texCoord;
}
`;
// WebGL2 MSDF片段着色器
const fragmentShaderSource = `#version 300 es
precision highp float;

in vec2 v_texCoord;

uniform sampler2D u_msdfTexture;
uniform vec3 u_color;
uniform vec3 u_outlineColor;
uniform float u_pxRange;
uniform float u_outlineWidth;
uniform float u_smoothness;

out vec4 fragColor;

// MSDF核心算法
float median(float r, float g, float b) {
    return max(min(r, g), min(max(r, g), b));
}

float screenPxRange() {
    vec2 unitRange = vec2(u_pxRange) / vec2(textureSize(u_msdfTexture, 0));
    vec2 screenTexSize = vec2(1.0) / fwidth(v_texCoord);
    return max(0.5 * dot(unitRange, screenTexSize), 1.0);
}

void main() {
    // 采样MSDF纹理
    vec3 msd = texture(u_msdfTexture, v_texCoord).rgb;
    float sd = median(msd.r, msd.g, msd.b);
    float screenPxDistance = screenPxRange() * (sd - 0.5);
    
    // 主文本渲染
    float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
    alpha = smoothstep(0.5 - u_smoothness, 0.5 + u_smoothness, sd);
    
    vec3 finalColor = u_color;
    float finalAlpha = alpha;
    
    // 描边效果
    if (u_outlineWidth > 0.0) {
        float outlineAlpha = smoothstep(0.5 - u_outlineWidth - u_smoothness, 
                                      0.5 - u_outlineWidth + u_smoothness, sd);
        finalColor = mix(u_outlineColor, u_color, alpha);
        finalAlpha = max(outlineAlpha, alpha);
    }
    
    fragColor = vec4(finalColor, finalAlpha);
}
`;
// 创建WebGL2着色器
const createShader = (gl, type, source) => {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        const error = gl.getShaderInfoLog(shader);
        gl.deleteShader(shader);
        throw new Error(`Shader compile error: ${error}`);
    }
    return shader;
};
// 创建WebGL2程序
const createProgram = (gl) => {
    const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        const error = gl.getProgramInfoLog(program);
        gl.deleteProgram(program);
        throw new Error(`Program link error: ${error}`);
    }
    gl.deleteShader(vertexShader);
    gl.deleteShader(fragmentShader);
    return program;
};
// 从MSDFAtlas创建WebGL2纹理
const createTextureFromAtlas = (gl, atlas) => {
    const texture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, texture);
    // 将Canvas数据上传到GPU
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGB, gl.RGB, gl.UNSIGNED_BYTE, atlas.canvas);
    // 设置纹理参数
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    return texture;
};
// 创建正交投影矩阵
const createOrthographicMatrix = (width, height) => {
    return new Float32Array([
        2 / width, 0, 0, 0,
        0, -2 / height, 0, 0,
        0, 0, -1, 0,
        -1, 1, 0, 1
    ]);
};
// 初始化WebGL2 MSDF渲染器
const initWebGL2MSDFRenderer = (canvas, atlas) => {
    const gl = canvas.getContext('webgl2');
    if (!gl) {
        throw new Error('WebGL2 not supported');
    }
    // 创建着色器程序
    const program = createProgram(gl);
    // 获取uniform位置
    const uniforms = {
        projection: gl.getUniformLocation(program, 'u_projection'),
        translation: gl.getUniformLocation(program, 'u_translation'),
        scale: gl.getUniformLocation(program, 'u_scale'),
        msdfTexture: gl.getUniformLocation(program, 'u_msdfTexture'),
        color: gl.getUniformLocation(program, 'u_color'),
        outlineColor: gl.getUniformLocation(program, 'u_outlineColor'),
        pxRange: gl.getUniformLocation(program, 'u_pxRange'),
        outlineWidth: gl.getUniformLocation(program, 'u_outlineWidth'),
        smoothness: gl.getUniformLocation(program, 'u_smoothness')
    };
    // 创建VAO
    const vao = gl.createVertexArray();
    gl.bindVertexArray(vao);
    // 创建缓冲区
    const vertexBuffer = gl.createBuffer();
    const indexBuffer = gl.createBuffer();
    // 设置顶点属性
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);
    gl.enableVertexAttribArray(texCoordLocation);
    gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
    // 创建纹理
    const texture = createTextureFromAtlas(gl, atlas);
    // 启用混合
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
    return {
        gl,
        program,
        vao,
        vertexBuffer,
        indexBuffer,
        texture,
        uniforms,
        atlas
    };
};
// 从MSDFAtlas生成文本几何数据
const generateTextGeometryFromAtlas = (text, atlas, fontSize) => {
    const vertices = [];
    const indices = [];
    const scale = fontSize / atlas.config.fontSize;
    let x = 0;
    let vertexIndex = 0;
    for (const char of text) {
        const charInfo = atlas.characters.get(char);
        if (!charInfo) {
            x += fontSize * 0.5; // 空格
            continue;
        }
        // 计算字符在屏幕上的位置和大小
        const charX = x + charInfo.xoffset * scale;
        const charY = charInfo.yoffset * scale;
        const charWidth = charInfo.width * scale;
        const charHeight = charInfo.height * scale;
        // 计算纹理坐标（基于atlas中的实际位置）
        const u1 = charInfo.x / atlas.config.atlasWidth;
        const v1 = charInfo.y / atlas.config.atlasHeight;
        const u2 = (charInfo.x + charInfo.width) / atlas.config.atlasWidth;
        const v2 = (charInfo.y + charInfo.height) / atlas.config.atlasHeight;
        // 添加四个顶点 (x, y, u, v)
        vertices.push(charX, charY, u1, v1, // 左下
        charX + charWidth, charY, u2, v1, // 右下
        charX + charWidth, charY + charHeight, u2, v2, // 右上
        charX, charY + charHeight, u1, v2 // 左上
        );
        // 添加两个三角形的索引
        indices.push(vertexIndex, vertexIndex + 1, vertexIndex + 2, vertexIndex, vertexIndex + 2, vertexIndex + 3);
        vertexIndex += 4;
        x += charInfo.xadvance * scale;
    }
    return {
        vertices: new Float32Array(vertices),
        indices: new Uint16Array(indices),
        width: x
    };
};
// 渲染文本
const renderTextWithAtlas = (context, text, options) => {
    const { gl, program, vao, vertexBuffer, indexBuffer, texture, uniforms, atlas } = context;
    // 生成几何数据
    const geometry = generateTextGeometryFromAtlas(text, atlas, options.fontSize);
    // 清除画布
    gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);
    gl.clearColor(0.1, 0.1, 0.1, 1);
    gl.clear(gl.COLOR_BUFFER_BIT);
    // 使用着色器程序
    gl.useProgram(program);
    gl.bindVertexArray(vao);
    // 更新缓冲区数据
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, geometry.vertices, gl.DYNAMIC_DRAW);
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, geometry.indices, gl.DYNAMIC_DRAW);
    // 设置uniforms
    const projectionMatrix = options.projection || createOrthographicMatrix(gl.canvas.width, gl.canvas.height);
    gl.uniformMatrix4fv(uniforms.projection, false, projectionMatrix);
    gl.uniform2f(uniforms.translation, options.x, options.y);
    gl.uniform1f(uniforms.scale, 1.0);
    gl.uniform3fv(uniforms.color, options.color);
    gl.uniform3fv(uniforms.outlineColor, options.outlineColor || [0, 0, 0]);
    gl.uniform1f(uniforms.pxRange, atlas.config.distanceRange);
    gl.uniform1f(uniforms.outlineWidth, options.outlineWidth || 0);
    gl.uniform1f(uniforms.smoothness, options.smoothness || 0.05);
    // 绑定纹理
    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.uniform1i(uniforms.msdfTexture, 0);
    // 绘制
    gl.drawElements(gl.TRIANGLES, geometry.indices.length, gl.UNSIGNED_SHORT, 0);
};
// 便捷函数：从generateMSDFAtlas结果创建完整的渲染系统
const createMSDFRendererFromAtlas = (canvas, atlas) => {
    const context = initWebGL2MSDFRenderer(canvas, atlas);
    return {
        context,
        render: (text, options) => {
            renderTextWithAtlas(context, text, options);
        },
        getAtlas: () => atlas,
        getSupportedCharacters: () => Array.from(atlas.characters.keys()),
        getMetrics: () => atlas.metrics,
        dispose: () => {
            const { gl, program, vao, vertexBuffer, indexBuffer, texture } = context;
            gl.deleteProgram(program);
            gl.deleteVertexArray(vao);
            gl.deleteBuffer(vertexBuffer);
            gl.deleteBuffer(indexBuffer);
            gl.deleteTexture(texture);
        }
    };
};
// 导出函数
export { initWebGL2MSDFRenderer, generateTextGeometryFromAtlas, renderTextWithAtlas, createMSDFRendererFromAtlas, createTextureFromAtlas };
