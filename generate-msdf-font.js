// MSDF字体生成工具
// 注意：这是一个简化的示例，实际生产中建议使用 msdf-atlas-gen 等专业工具

class MSDFFontGenerator {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
    }

    // 生成中文字符的MSDF图集
    async generateChineseFontAtlas(characters, fontFamily = 'SimHei', fontSize = 64) {
        const atlasSize = this.calculateAtlasSize(characters.length);
        const cellSize = Math.floor(atlasSize / Math.ceil(Math.sqrt(characters.length)));
        
        this.canvas.width = atlasSize;
        this.canvas.height = atlasSize;
        
        const metrics = {
            atlas: {
                type: "msdf",
                distanceRange: 4,
                size: fontSize,
                width: atlasSize,
                height: atlasSize
            },
            metrics: {
                emSize: fontSize,
                lineHeight: fontSize * 1.2,
                ascender: fontSize * 0.8,
                descender: fontSize * 0.2,
                underlineY: fontSize * 0.1,
                underlineThickness: fontSize * 0.05
            },
            glyphs: []
        };

        let x = 0, y = 0;
        const cols = Math.ceil(atlasSize / cellSize);

        for (let i = 0; i < characters.length; i++) {
            const char = characters[i];
            const charCode = char.charCodeAt(0);
            
            // 计算当前字符在图集中的位置
            const col = i % cols;
            const row = Math.floor(i / cols);
            const cellX = col * cellSize;
            const cellY = row * cellSize;
            
            // 生成字符的MSDF数据
            const glyphData = this.generateCharacterMSDF(char, fontFamily, fontSize, cellSize);
            
            // 将字符绘制到图集中
            this.drawCharacterToAtlas(glyphData, cellX, cellY, cellSize);
            
            // 添加字符度量信息
            const glyph = {
                unicode: charCode,
                advance: glyphData.advance,
                planeBounds: {
                    left: 0,
                    bottom: 0,
                    right: glyphData.width,
                    top: glyphData.height
                },
                atlasBounds: {
                    left: cellX,
                    bottom: cellY,
                    right: cellX + cellSize,
                    top: cellY + cellSize
                }
            };
            
            metrics.glyphs.push(glyph);
        }

        return {
            atlas: this.canvas,
            metrics: metrics
        };
    }

    // 计算图集大小
    calculateAtlasSize(charCount) {
        const minSize = 512;
        const maxSize = 2048;
        const cellsPerSide = Math.ceil(Math.sqrt(charCount));
        const requiredSize = Math.pow(2, Math.ceil(Math.log2(cellsPerSide * 64)));
        return Math.max(minSize, Math.min(maxSize, requiredSize));
    }

    // 生成单个字符的MSDF数据
    generateCharacterMSDF(char, fontFamily, fontSize, cellSize) {
        // 创建临时canvas用于字符渲染
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        
        tempCanvas.width = cellSize;
        tempCanvas.height = cellSize;
        
        // 设置字体
        tempCtx.font = `${fontSize}px ${fontFamily}`;
        tempCtx.textAlign = 'center';
        tempCtx.textBaseline = 'middle';
        tempCtx.fillStyle = 'white';
        
        // 测量字符
        const metrics = tempCtx.measureText(char);
        const width = metrics.width;
        const height = fontSize;
        
        // 清除并绘制字符
        tempCtx.clearRect(0, 0, cellSize, cellSize);
        tempCtx.fillText(char, cellSize / 2, cellSize / 2);
        
        // 获取像素数据
        const imageData = tempCtx.getImageData(0, 0, cellSize, cellSize);
        
        // 生成距离场数据（简化版本）
        const msdfData = this.generateDistanceField(imageData, cellSize);
        
        return {
            char: char,
            width: width,
            height: height,
            advance: width * 1.1, // 字符间距
            msdfData: msdfData
        };
    }

    // 生成距离场（简化版本）
    generateDistanceField(imageData, size) {
        const data = imageData.data;
        const msdfData = new Uint8Array(size * size * 3);
        
        for (let y = 0; y < size; y++) {
            for (let x = 0; x < size; x++) {
                const index = (y * size + x) * 4;
                const alpha = data[index + 3];
                
                // 简化的距离场计算
                // 实际MSDF需要更复杂的算法
                const distance = this.calculateSimpleDistance(x, y, data, size);
                const normalizedDistance = Math.max(0, Math.min(1, 0.5 + distance / 32));
                const value = Math.floor(normalizedDistance * 255);
                
                const msdfIndex = (y * size + x) * 3;
                msdfData[msdfIndex] = value;     // R
                msdfData[msdfIndex + 1] = value; // G
                msdfData[msdfIndex + 2] = value; // B
            }
        }
        
        return msdfData;
    }

    // 简化的距离计算
    calculateSimpleDistance(x, y, data, size) {
        const currentIndex = (y * size + x) * 4;
        const currentAlpha = data[currentIndex + 3];
        const isInside = currentAlpha > 128;
        
        let minDistance = Infinity;
        const searchRadius = 16;
        
        for (let dy = -searchRadius; dy <= searchRadius; dy++) {
            for (let dx = -searchRadius; dx <= searchRadius; dx++) {
                const nx = x + dx;
                const ny = y + dy;
                
                if (nx < 0 || nx >= size || ny < 0 || ny >= size) continue;
                
                const neighborIndex = (ny * size + nx) * 4;
                const neighborAlpha = data[neighborIndex + 3];
                const neighborIsInside = neighborAlpha > 128;
                
                if (isInside !== neighborIsInside) {
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    minDistance = Math.min(minDistance, distance);
                }
            }
        }
        
        return isInside ? minDistance : -minDistance;
    }

    // 将字符绘制到图集
    drawCharacterToAtlas(glyphData, x, y, cellSize) {
        const imageData = this.ctx.createImageData(cellSize, cellSize);
        const data = imageData.data;
        
        // 将MSDF数据复制到图集
        for (let i = 0; i < glyphData.msdfData.length; i += 3) {
            const pixelIndex = Math.floor(i / 3) * 4;
            data[pixelIndex] = glyphData.msdfData[i];     // R
            data[pixelIndex + 1] = glyphData.msdfData[i + 1]; // G
            data[pixelIndex + 2] = glyphData.msdfData[i + 2]; // B
            data[pixelIndex + 3] = 255; // A
        }
        
        this.ctx.putImageData(imageData, x, y);
    }

    // 导出图集为PNG
    exportAtlasToPNG(canvas) {
        return new Promise((resolve) => {
            canvas.toBlob(resolve, 'image/png');
        });
    }

    // 导出度量数据为JSON
    exportMetricsToJSON(metrics) {
        return JSON.stringify(metrics, null, 2);
    }
}

// 常用中文字符集
const COMMON_CHINESE_CHARS = [
    // 基础汉字
    '你', '好', '世', '界', '中', '文', '字', '体', '测', '试',
    '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
    '大', '小', '人', '天', '地', '上', '下', '左', '右', '前',
    '后', '里', '外', '东', '西', '南', '北', '今', '明', '昨',
    '年', '月', '日', '时', '分', '秒', '春', '夏', '秋', '冬',
    
    // 常用词汇
    '的', '了', '在', '是', '我', '有', '他', '这', '个', '们',
    '来', '到', '时', '大', '地', '为', '子', '中', '你', '说',
    '生', '国', '年', '着', '就', '那', '和', '要', '她', '出',
    '也', '得', '里', '后', '自', '以', '会', '家', '可', '下',
    
    // 数字和标点
    '０', '１', '２', '３', '４', '５', '６', '７', '８', '９',
    '，', '。', '！', '？', '：', '；', '"', '"', '\'',
    '（', '）', '【', '】', '《', '》', '、', '·'
];

// 使用示例函数
async function generateChineseMSDFFont() {
    const generator = new MSDFFontGenerator();
    
    console.log('开始生成中文MSDF字体...');
    
    try {
        const result = await generator.generateChineseFontAtlas(
            COMMON_CHINESE_CHARS,
            'SimHei', // 黑体
            64
        );
        
        console.log('字体生成完成！');
        
        // 导出图集
        const atlasBlob = await generator.exportAtlasToPNG(result.atlas);
        const atlasUrl = URL.createObjectURL(atlasBlob);
        
        // 导出度量数据
        const metricsJson = generator.exportMetricsToJSON(result.metrics);
        const metricsBlob = new Blob([metricsJson], { type: 'application/json' });
        const metricsUrl = URL.createObjectURL(metricsBlob);
        
        // 创建下载链接
        const downloadAtlas = document.createElement('a');
        downloadAtlas.href = atlasUrl;
        downloadAtlas.download = 'chinese-msdf-atlas.png';
        downloadAtlas.textContent = '下载字体图集';
        downloadAtlas.style.display = 'block';
        downloadAtlas.style.margin = '10px';
        
        const downloadMetrics = document.createElement('a');
        downloadMetrics.href = metricsUrl;
        downloadMetrics.download = 'chinese-msdf-metrics.json';
        downloadMetrics.textContent = '下载字体度量数据';
        downloadMetrics.style.display = 'block';
        downloadMetrics.style.margin = '10px';
        
        // 显示预览
        const preview = document.createElement('div');
        preview.innerHTML = `
            <h3>生成的MSDF字体预览</h3>
            <p>字符数量: ${COMMON_CHINESE_CHARS.length}</p>
            <p>图集大小: ${result.atlas.width}x${result.atlas.height}</p>
        `;
        
        const previewImage = document.createElement('img');
        previewImage.src = atlasUrl;
        previewImage.style.maxWidth = '100%';
        previewImage.style.border = '1px solid #ccc';
        
        document.body.appendChild(preview);
        document.body.appendChild(previewImage);
        document.body.appendChild(downloadAtlas);
        document.body.appendChild(downloadMetrics);
        
        return result;
        
    } catch (error) {
        console.error('字体生成失败:', error);
        throw error;
    }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.generateChineseMSDFFont = generateChineseMSDFFont;
    window.MSDFFontGenerator = MSDFFontGenerator;
    window.COMMON_CHINESE_CHARS = COMMON_CHINESE_CHARS;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MSDFFontGenerator,
        generateChineseMSDFFont,
        COMMON_CHINESE_CHARS
    };
}
