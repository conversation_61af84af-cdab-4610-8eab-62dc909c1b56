<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSDF中文渲染示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        
        canvas {
            border: 1px solid #333;
            background: #000;
            display: block;
            margin: 20px auto;
        }
        
        .controls {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        
        .control-group {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        label {
            min-width: 100px;
            color: #ccc;
        }
        
        input[type="range"] {
            flex: 1;
            max-width: 200px;
        }
        
        input[type="text"] {
            flex: 1;
            max-width: 300px;
            padding: 5px;
            background: #333;
            border: 1px solid #555;
            color: white;
            border-radius: 4px;
        }
        
        .value {
            min-width: 60px;
            color: #fff;
            font-weight: bold;
        }
        
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h2>MSDF中文字体渲染</h2>
        
        <div class="control-group">
            <label>文本内容:</label>
            <input type="text" id="textInput" value="你好世界！Hello MSDF" placeholder="输入要渲染的文本">
        </div>
        
        <div class="control-group">
            <label>字体大小:</label>
            <input type="range" id="fontSizeSlider" min="12" max="120" value="48">
            <span class="value" id="fontSizeValue">48px</span>
        </div>
        
        <div class="control-group">
            <label>描边宽度:</label>
            <input type="range" id="outlineSlider" min="0" max="8" step="0.1" value="1">
            <span class="value" id="outlineValue">1.0</span>
        </div>
        
        <div class="control-group">
            <label>平滑度:</label>
            <input type="range" id="smoothnessSlider" min="0.1" max="2" step="0.1" value="1">
            <span class="value" id="smoothnessValue">1.0</span>
        </div>
        
        <div class="control-group">
            <label>颜色:</label>
            <input type="color" id="colorPicker" value="#ffffff">
        </div>
    </div>
    
    <canvas id="canvas" width="800" height="400"></canvas>
    
    <div class="controls">
        <div class="info">
            <h3>关于MSDF渲染</h3>
            <p>MSDF (Multi-channel Signed Distance Field) 是一种高质量的文本渲染技术，特别适合：</p>
            <ul>
                <li>任意缩放而不失真的矢量文本</li>
                <li>复杂的中文字符渲染</li>
                <li>高效的GPU渲染性能</li>
                <li>支持描边、阴影等效果</li>
            </ul>
            <p>本示例使用WebGL2实现MSDF渲染，支持实时调整字体大小、描边和平滑度。</p>
        </div>
    </div>

    <script type="module">
        // 导入MSDF相关模块
        import { MSDFFontLoader, MSDFTextRenderer } from './msdf-font-loader.js';
        import { MSDFFontGenerator, COMMON_CHINESE_CHARS } from './generate-msdf-font.js';

        // WebGL2 MSDF渲染器
        class MSDFRenderer {
            constructor(canvas) {
                this.canvas = canvas;
                this.gl = canvas.getContext('webgl2');
                if (!this.gl) {
                    throw new Error('WebGL2 not supported');
                }
                
                this.program = null;
                this.vao = null;
                this.vertexBuffer = null;
                this.indexBuffer = null;
                this.msdfTexture = null;
                
                this.init();
            }
            
            init() {
                const gl = this.gl;
                
                // 顶点着色器
                const vertexShaderSource = `#version 300 es
                    in vec2 a_position;
                    in vec2 a_texCoord;
                    
                    uniform vec2 u_resolution;
                    uniform vec2 u_translation;
                    uniform float u_scale;
                    
                    out vec2 v_texCoord;
                    
                    void main() {
                        vec2 position = (a_position * u_scale + u_translation) / u_resolution * 2.0 - 1.0;
                        gl_Position = vec4(position.x, -position.y, 0.0, 1.0);
                        v_texCoord = a_texCoord;
                    }
                `;
                
                // 片段着色器 - MSDF实现
                const fragmentShaderSource = `#version 300 es
                    precision highp float;
                    
                    in vec2 v_texCoord;
                    
                    uniform sampler2D u_msdfTexture;
                    uniform vec3 u_color;
                    uniform float u_pxRange;
                    uniform float u_outlineWidth;
                    uniform float u_smoothness;
                    
                    out vec4 fragColor;
                    
                    float median(float r, float g, float b) {
                        return max(min(r, g), min(max(r, g), b));
                    }
                    
                    void main() {
                        vec3 msd = texture(u_msdfTexture, v_texCoord).rgb;
                        float sd = median(msd.r, msd.g, msd.b);
                        
                        float screenPxDistance = u_pxRange * (sd - 0.5);
                        float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
                        
                        // 应用平滑度
                        alpha = smoothstep(0.5 - u_smoothness * 0.1, 0.5 + u_smoothness * 0.1, sd);
                        
                        // 描边效果
                        if (u_outlineWidth > 0.0) {
                            float outlineAlpha = smoothstep(0.5 - u_outlineWidth * 0.1, 0.5, sd);
                            alpha = max(alpha, outlineAlpha * 0.5);
                        }
                        
                        fragColor = vec4(u_color, alpha);
                    }
                `;
                
                // 编译着色器
                this.program = this.createProgram(vertexShaderSource, fragmentShaderSource);
                
                // 获取uniform位置
                this.uniforms = {
                    resolution: gl.getUniformLocation(this.program, 'u_resolution'),
                    translation: gl.getUniformLocation(this.program, 'u_translation'),
                    scale: gl.getUniformLocation(this.program, 'u_scale'),
                    msdfTexture: gl.getUniformLocation(this.program, 'u_msdfTexture'),
                    color: gl.getUniformLocation(this.program, 'u_color'),
                    pxRange: gl.getUniformLocation(this.program, 'u_pxRange'),
                    outlineWidth: gl.getUniformLocation(this.program, 'u_outlineWidth'),
                    smoothness: gl.getUniformLocation(this.program, 'u_smoothness')
                };
                
                // 创建VAO
                this.vao = gl.createVertexArray();
                gl.bindVertexArray(this.vao);
                
                // 创建缓冲区
                this.vertexBuffer = gl.createBuffer();
                this.indexBuffer = gl.createBuffer();
                
                // 设置顶点属性
                const positionLocation = gl.getAttribLocation(this.program, 'a_position');
                const texCoordLocation = gl.getAttribLocation(this.program, 'a_texCoord');
                
                gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
                gl.enableVertexAttribArray(positionLocation);
                gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);
                gl.enableVertexAttribArray(texCoordLocation);
                gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);
                
                gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
                
                // 创建模拟的MSDF纹理
                this.createMSDFTexture();
                
                // 设置混合
                gl.enable(gl.BLEND);
                gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
            }
            
            createProgram(vertexSource, fragmentSource) {
                const gl = this.gl;
                
                const vertexShader = this.createShader(gl.VERTEX_SHADER, vertexSource);
                const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fragmentSource);
                
                const program = gl.createProgram();
                gl.attachShader(program, vertexShader);
                gl.attachShader(program, fragmentShader);
                gl.linkProgram(program);
                
                if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                    throw new Error('Program link error: ' + gl.getProgramInfoLog(program));
                }
                
                return program;
            }
            
            createShader(type, source) {
                const gl = this.gl;
                const shader = gl.createShader(type);
                gl.shaderSource(shader, source);
                gl.compileShader(shader);
                
                if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                    throw new Error('Shader compile error: ' + gl.getShaderInfoLog(shader));
                }
                
                return shader;
            }
            
            createMSDFTexture() {
                const gl = this.gl;
                
                // 创建一个简单的MSDF纹理数据（模拟）
                // 实际应用中，这应该是从字体文件生成的MSDF图集
                const size = 512;
                const data = new Uint8Array(size * size * 3);
                
                // 生成一些简单的字符形状作为演示
                this.generateSimpleMSDFData(data, size);
                
                this.msdfTexture = gl.createTexture();
                gl.bindTexture(gl.TEXTURE_2D, this.msdfTexture);
                gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGB, size, size, 0, gl.RGB, gl.UNSIGNED_BYTE, data);
                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
            }
            
            generateSimpleMSDFData(data, size) {
                // 生成简单的矩形字符作为MSDF数据
                for (let y = 0; y < size; y++) {
                    for (let x = 0; x < size; x++) {
                        const index = (y * size + x) * 3;
                        
                        // 计算到矩形边缘的距离
                        const centerX = size * 0.5;
                        const centerY = size * 0.5;
                        const rectWidth = size * 0.6;
                        const rectHeight = size * 0.6;
                        
                        const dx = Math.max(0, Math.abs(x - centerX) - rectWidth * 0.5);
                        const dy = Math.max(0, Math.abs(y - centerY) - rectHeight * 0.5);
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        
                        // 转换为MSDF值 (0.5为边界)
                        const normalizedDistance = Math.max(0, Math.min(1, 0.5 + (20 - distance) / 40));
                        const value = Math.floor(normalizedDistance * 255);
                        
                        data[index] = value;     // R
                        data[index + 1] = value; // G  
                        data[index + 2] = value; // B
                    }
                }
            }
            
            render(text, x, y, scale, color, outlineWidth, smoothness) {
                const gl = this.gl;
                
                gl.viewport(0, 0, this.canvas.width, this.canvas.height);
                gl.clearColor(0, 0, 0, 1);
                gl.clear(gl.COLOR_BUFFER_BIT);
                
                gl.useProgram(this.program);
                gl.bindVertexArray(this.vao);
                
                // 设置uniforms
                gl.uniform2f(this.uniforms.resolution, this.canvas.width, this.canvas.height);
                gl.uniform2f(this.uniforms.translation, x, y);
                gl.uniform1f(this.uniforms.scale, scale);
                gl.uniform3f(this.uniforms.color, color[0], color[1], color[2]);
                gl.uniform1f(this.uniforms.pxRange, 4.0);
                gl.uniform1f(this.uniforms.outlineWidth, outlineWidth);
                gl.uniform1f(this.uniforms.smoothness, smoothness);
                
                // 绑定纹理
                gl.activeTexture(gl.TEXTURE0);
                gl.bindTexture(gl.TEXTURE_2D, this.msdfTexture);
                gl.uniform1i(this.uniforms.msdfTexture, 0);
                
                // 为每个字符渲染一个四边形
                const charWidth = 64;
                const charHeight = 64;
                
                for (let i = 0; i < text.length; i++) {
                    const offsetX = i * charWidth * 0.8;
                    
                    // 顶点数据 (position + texCoord)
                    const vertices = new Float32Array([
                        offsetX, 0, 0, 0,
                        offsetX + charWidth, 0, 1, 0,
                        offsetX + charWidth, charHeight, 1, 1,
                        offsetX, charHeight, 0, 1
                    ]);
                    
                    const indices = new Uint16Array([0, 1, 2, 0, 2, 3]);
                    
                    gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
                    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.DYNAMIC_DRAW);
                    
                    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
                    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.DYNAMIC_DRAW);
                    
                    gl.drawElements(gl.TRIANGLES, 6, gl.UNSIGNED_SHORT, 0);
                }
            }
        }
        
        // 初始化应用
        const canvas = document.getElementById('canvas');
        const renderer = new MSDFRenderer(canvas);
        
        // 控制元素
        const textInput = document.getElementById('textInput');
        const fontSizeSlider = document.getElementById('fontSizeSlider');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const outlineSlider = document.getElementById('outlineSlider');
        const outlineValue = document.getElementById('outlineValue');
        const smoothnessSlider = document.getElementById('smoothnessSlider');
        const smoothnessValue = document.getElementById('smoothnessValue');
        const colorPicker = document.getElementById('colorPicker');
        
        // 渲染函数
        function render() {
            const text = textInput.value;
            const fontSize = parseInt(fontSizeSlider.value);
            const outline = parseFloat(outlineSlider.value);
            const smoothness = parseFloat(smoothnessSlider.value);
            const colorHex = colorPicker.value;
            
            // 转换颜色
            const r = parseInt(colorHex.substr(1, 2), 16) / 255;
            const g = parseInt(colorHex.substr(3, 2), 16) / 255;
            const b = parseInt(colorHex.substr(5, 2), 16) / 255;
            
            const scale = fontSize / 64;
            const x = 50;
            const y = canvas.height / 2 - fontSize / 2;
            
            renderer.render(text, x, y, scale, [r, g, b], outline, smoothness);
        }
        
        // 事件监听
        textInput.addEventListener('input', render);
        fontSizeSlider.addEventListener('input', () => {
            fontSizeValue.textContent = fontSizeSlider.value + 'px';
            render();
        });
        outlineSlider.addEventListener('input', () => {
            outlineValue.textContent = outlineSlider.value;
            render();
        });
        smoothnessSlider.addEventListener('input', () => {
            smoothnessValue.textContent = smoothnessSlider.value;
            render();
        });
        colorPicker.addEventListener('input', render);
        
        // 初始渲染
        render();
        
        // 处理canvas大小变化
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * devicePixelRatio;
            canvas.height = rect.height * devicePixelRatio;
            render();
        }
        
        window.addEventListener('resize', resizeCanvas);
    </script>
</body>
</html>
