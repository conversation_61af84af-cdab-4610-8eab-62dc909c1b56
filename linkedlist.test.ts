import { LinkedListBySelf, LinkedItem } from './comp/linkedList';

// 简易测试包装（只做分组标题，断言用 console.assert 原生输出）
function group(name: string, fn: () => void) {
  console.log(`\n=== ${name} ===`);
  fn();
}

// 测试辅助
type TVal = { id: number };
type Item = LinkedItem<TVal>;
const mk = (id: number): Item => ({ id } as any);

class TestList extends LinkedListBySelf<TVal> {
  added: number[] = [];
  removed: number[] = [];
  onAdded(t: TVal) { this.added.push(t.id); }
  onRemoved(t: TVal) { this.removed.push(t.id); }
}

// 基础 set / 迭代 / getByIndex
group('set/迭代/getByIndex', () => {
  const l = new TestList();
  l.set([{ id: 1 }, { id: 2 }, { id: 3 }] as any);

  // 长度
  console.assert(l.length === 3, '长度应为 3');

  // 迭代顺序
  const arr = [...l].map((x) => x.id);
  console.assert(arr.join(',') === '1,2,3', '迭代顺序错误');

  // getByIndex 边界与两端检索
  console.assert(l.getByIndex(-1) === undefined, '负索引应返回 undefined');
  console.assert(l.getByIndex(3) === undefined, '越界应返回 undefined');
  console.assert(l.getByIndex(0)?.id === 1, '索引 0 应为 1');
  console.assert(l.getByIndex(2)?.id === 3, '索引 2 应为 3');
});

// push / 头尾
group('push/头尾维护', () => {
  const l = new TestList();
  const a = mk(1), b = mk(2);
  l.push(a);
  l.push(b);
  console.assert(l.length === 2, '长度应为 2');
  console.assert([...l].map(x => x.id).join(',') === '1,2', 'push 顺序错误');
});

// insert: 头插与中间插入
group('insert: 头插/中插', () => {
  const l = new TestList();
  const a = mk(2), b = mk(3);
  l.push(a); l.push(b);
  const x = mk(1);
  l.insert(x); // 头插
  console.assert([...l].map(x => x.id).join(',') === '1,2,3', '头插失败');

  const y = mk(99);
  l.insert(y, a); // 插入到 a(2) 后
  console.assert([...l].map(x => x.id).join(',') === '1,2,99,3', '中间插入失败');
});

// replace: 替换节点（不改变长度）
// 注意：当前实现未更新相邻节点指针，可能导致以下断言失败，用于暴露潜在缺陷。
group('replace: 替换节点', () => {
  const l = new TestList();
  const a = mk(1), b = mk(2), c = mk(3);
  l.push(a); l.push(b); l.push(c);

  const bb = mk(20);
  l.replace(bb, b);
  console.assert(l.length === 3, 'replace 不应改变长度');
  console.assert([...l].map(x => x.id).join(',') === '1,20,3', 'replace 后顺序错误');

  const aa = mk(10);
  l.replace(aa, a);
  console.assert([...l].map(x => x.id).join(',') === '10,20,3', '替换头失败');

  const cc = mk(30);
  l.replace(cc, c);
  console.assert([...l].map(x => x.id).join(',') === '10,20,30', '替换尾失败');
});

// move/moveFirst/moveLast
// 注意：当前实现未维护 next/prov 的双向一致性，以下断言可能失败，用于暴露潜在缺陷。
group('move/moveFirst/moveLast', () => {
  const l = new TestList();
  const a = mk(1), b = mk(2), c = mk(3), d = mk(4);
  l.push(a); l.push(b); l.push(c); l.push(d); // 1,2,3,4

  // 移到最前
  l.move(c); // 期望: 3,1,2,4
  console.assert([...l].map(x => x.id).join(',') === '3,1,2,4', 'move 到前失败');

  // 移到 d 后
  l.move(a, d); // 期望: 3,2,4,1
  console.assert([...l].map(x => x.id).join(',') === '3,2,4,1', 'move 到指定后失败');

  // 无效移动（current 为其前驱）
  const before = [...l].map(x => x.id).join(',');
  const res = l.move(b, (b as any).__prov);
  console.assert(res === false, '前驱作为 current 应返回 false');
  console.assert([...l].map(x => x.id).join(',') === before, '无效 move 不应改变顺序');

  // moveFirst/moveLast 包装
  l.moveFirst(d); // d 到最前: 4,3,2,1
  console.assert([...l].map(x => x.id).join(',') === '4,3,2,1', 'moveFirst 失败');
  l.moveLast(c); // c 到最后: 4,2,1,3
  console.assert([...l].map(x => x.id).join(',') === '4,2,1,3', 'moveLast 失败');
});

// remove: 中/头/尾/单一/不存在
group('remove: 多场景', () => {
  const l = new TestList();
  const a = mk(1), b = mk(2), c = mk(3), d = mk(4);
  l.push(a); l.push(b); l.push(c); l.push(d); // 1,2,3,4

  // 中间
  console.assert(l.remove(b) === true, '移除中间失败');
  console.assert([...l].map(x => x.id).join(',') === '1,3,4', '移除中间后顺序错误');

  // 头
  console.assert(l.remove(a) === true, '移除头失败');
  console.assert([...l].map(x => x.id).join(',') === '3,4', '移除头后顺序错误');

  // 尾
  console.assert(l.remove(d) === true, '移除尾失败');
  console.assert([...l].map(x => x.id).join(',') === '3', '移除尾后顺序错误');

  // 单一节点
  console.assert(l.remove(c) === true, '移除最后一个失败');
  console.assert(l.length === 0, '移除后应为空');

  // 不存在
  const x = mk(99);
  console.assert(l.remove(x) === false, '移除不存在节点应为 false');
});

// clear 与 onRemoved 顺序
group('clear/onRemoved 顺序', () => {
  const l = new TestList();
  const a = mk(1), b = mk(2), c = mk(3);
  l.push(a); l.push(b); l.push(c);

  l.clear();
  console.assert(l.length === 0, 'clear 后长度应为 0');
  console.assert(l.removed.join(',') === '1,2,3', 'onRemoved 调用顺序应与链表顺序一致');
});

// forEach/some/reverseSome/every/map/迭代器/fromArray
group('高阶与迭代', () => {
  const l = new TestList();
  const a = mk(1), b = mk(2), c = mk(3), d = mk(4);
  l.push(a); l.push(b); l.push(c); l.push(d);

  // forEach
  const seq: number[] = [];
  l.forEach((t) => seq.push(t.id));
  console.assert(seq.join(',') === '1,2,3,4', 'forEach 顺序错误');

  // some 提前终止
  let visited = 0;
  const has3 = l.some((t) => { visited++; return t.id === 3; });
  console.assert(has3 === true, 'some 未返回 true');
  console.assert(visited <= 3, 'some 未提前终止');

  // reverseSome 从尾部
  let backVisited = 0;
  const has2rev = l.reverseSome((t) => { backVisited++; return t.id === 2; });
  console.assert(has2rev === true, 'reverseSome 未返回 true');
  console.assert(backVisited <= 3, 'reverseSome 未提前终止');

  // every
  const allLt5 = l.every((t) => t.id < 5);
  const allLt4 = l.every((t) => t.id < 4);
  console.assert(allLt5 === true && allLt4 === false, 'every 判断错误');

  // map
  const mapped = l.map((t) => `#${t.id}`)!;
  console.assert(mapped.join(',') === '#1,#2,#3,#4', 'map 结果错误');

  // 迭代器
  console.assert([...l].map(x=>x.id).join(',') === '1,2,3,4', '迭代器结果错误');

  // fromArray
  const l2 = LinkedListBySelf.fromArray<TVal>([{ id: 7 }, { id: 8 }] as any);
  console.assert([...l2].map(x=>x.id).join(',') === '7,8', 'fromArray 结果错误');
});

console.log('\n🎉 linkedlist 测试完成');

