# Custom CSS/JS Injector

一个 Chrome 扩展，允许用户为指定网站自定义注入 CSS 样式和 JavaScript 脚本。

## 功能特性

- 🎯 **精确匹配**：支持通配符模式匹配特定网站
- 🎨 **CSS 注入**：为目标网站注入自定义样式
- ⚡ **JS 注入**：在页面加载时执行自定义脚本
- 🔧 **可视化配置**：友好的选项页面，无需编辑代码
- 💾 **云同步**：配置自动同步到 Chrome 账户
- 🚫 **默认无操作**：安装后不会影响任何网站，需用户主动配置

## 安装方法

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择此 `chrome-plugin` 目录

## 使用方法

1. 安装后点击扩展图标，或右键选择"选项"
2. 在选项页面中：
   - 点击"+ 添加新规则"
   - 勾选"启用"复选框
   - 填写 URL 模式（支持通配符 `*`）
   - 在 CSS 区域输入样式代码
   - 在 JavaScript 区域输入脚本代码
3. 点击"保存配置"
4. 访问匹配的网站查看效果

## URL 模式示例

- `https://example.com/*` - 匹配 example.com 下所有页面
- `*://github.com/*/issues` - 匹配 GitHub 的 issues 页面
- `https://www.google.com/search*` - 匹配 Google 搜索页面
- `*://*.youtube.com/*` - 匹配所有 YouTube 页面

## CSS 示例

```css
/* 隐藏广告 */
[id*="ad"], [class*="ad"] {
  display: none !important;
}

/* 修改主题色 */
:root {
  --primary-color: #007bff !important;
}

/* 自定义字体 */
body {
  font-family: "SF Pro Display", -apple-system, sans-serif !important;
}
```

## JavaScript 示例

```javascript
// 移除所有广告元素
document.querySelectorAll('[id*="ad"], [class*="ad"]').forEach(el => el.remove());

// 修改页面标题
document.title = "自定义标题 - " + document.title;

// 添加自定义按钮
const btn = document.createElement('button');
btn.textContent = '自定义按钮';
btn.onclick = () => alert('Hello!');
document.body.appendChild(btn);
```

## 测试方法

1. 在浏览器中打开 `chrome-plugin/test.html` 文件
2. 在扩展选项中添加规则：
   - URL 模式：`file://*test.html`
   - 添加测试 CSS 和 JavaScript 代码
3. 保存配置后刷新测试页面查看效果

## 注意事项

- CSS 建议使用 `!important` 确保样式优先级
- JavaScript 在页面上下文中执行，可访问页面的 DOM 和变量
- 配置会自动同步到 Chrome 账户
- 扩展需要 `<all_urls>` 权限以支持任意网站注入
- **已修复 CSP 问题**：使用 `code` 参数代替 `eval()` 进行脚本注入

## 文件结构

```
chrome-plugin/
├── manifest.json      # 扩展清单
├── background.js      # 后台脚本，处理注入逻辑
├── options.html       # 选项页面 HTML
├── options.js         # 选项页面逻辑
├── styles.css         # 已废弃，保留兼容性
└── README.md          # 说明文档
```
