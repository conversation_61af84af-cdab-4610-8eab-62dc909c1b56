// 页面加载时读取已保存的规则
document.addEventListener('DOMContentLoaded', loadRules);

// 添加新规则按钮
document.getElementById('addRule').addEventListener('click', () => {
  addRuleElement({
    enabled: true,
    pattern: '',
    css: '',
    js: ''
  });
});

// 保存按钮
document.getElementById('save').addEventListener('click', saveRules);

// 从存储加载规则
async function loadRules() {
  try {
    const { rules = [] } = await chrome.storage.sync.get('rules');
    
    if (rules.length === 0) {
      // 默认添加一个空规则作为示例
      addRuleElement({
        enabled: false,
        pattern: 'https://example.com/*',
        css: '/* 在这里写 CSS */\nbody { background: #f0f0f0 !important; }',
        js: '// 在这里写 JavaScript\nconsole.log("Custom script loaded");'
      });
    } else {
      rules.forEach(rule => addRuleElement(rule));
    }
  } catch (error) {
    console.error('Failed to load rules:', error);
  }
}

// 添加规则元素到页面
function addRuleElement(rule) {
  const rulesContainer = document.getElementById('rules');
  const ruleDiv = document.createElement('div');
  ruleDiv.className = 'rule';
  
  ruleDiv.innerHTML = `
    <div class="rule-header">
      <input type="checkbox" ${rule.enabled ? 'checked' : ''}>
      <input type="text" placeholder="URL 模式 (如: https://example.com/*)" value="${rule.pattern}">
      <button onclick="this.parentElement.parentElement.remove()">删除</button>
    </div>
    <div class="textarea-label">CSS 样式：</div>
    <textarea placeholder="在这里输入 CSS 代码...">${rule.css}</textarea>
    <div class="textarea-label">JavaScript 脚本：</div>
    <textarea placeholder="在这里输入 JavaScript 代码...">${rule.js}</textarea>
  `;
  
  rulesContainer.appendChild(ruleDiv);
}

// 保存所有规则
async function saveRules() {
  const rules = [];
  const ruleElements = document.querySelectorAll('.rule');
  
  ruleElements.forEach(ruleElement => {
    const checkbox = ruleElement.querySelector('input[type="checkbox"]');
    const patternInput = ruleElement.querySelector('input[type="text"]');
    const textareas = ruleElement.querySelectorAll('textarea');
    
    const rule = {
      enabled: checkbox.checked,
      pattern: patternInput.value.trim(),
      css: textareas[0].value.trim(),
      js: textareas[1].value.trim()
    };
    
    // 只保存有效的规则（至少有 pattern）
    if (rule.pattern) {
      rules.push(rule);
    }
  });
  
  try {
    await chrome.storage.sync.set({ rules });
    showStatus('配置已保存！', 'success');
  } catch (error) {
    console.error('Failed to save rules:', error);
    showStatus('保存失败：' + error.message, 'error');
  }
}

// 显示状态消息
function showStatus(message, type) {
  const status = document.getElementById('status');
  status.textContent = message;
  status.style.color = type === 'success' ? '#28a745' : '#dc3545';
  status.style.fontWeight = 'bold';
  
  setTimeout(() => {
    status.textContent = '';
  }, 3000);
}
