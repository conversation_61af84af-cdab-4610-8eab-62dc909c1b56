// 监听页面加载，检查是否有匹配的规则需要注入
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status !== 'loading' || !tab.url) return;
  
  try {
    const { rules = [] } = await chrome.storage.sync.get('rules');
    const matchedRules = rules.filter(rule => 
      rule.enabled && matchesPattern(tab.url, rule.pattern)
    );
    
    for (const rule of matchedRules) {
      // 注入 CSS
      if (rule.css && rule.css.trim()) {
        await chrome.scripting.insertCSS({
          target: { tabId },
          css: rule.css
        });
      }
      
      // 注入 JS - 直接作为代码字符串注入
      if (rule.js && rule.js.trim()) {
        try {
          // 将用户代码包装在 IIFE 中，避免全局污染
          const wrappedCode = `
            (function() {
              try {
                ${rule.js}
              } catch (e) {
                console.error('Custom script error:', e);
              }
            })();
          `;

          await chrome.scripting.executeScript({
            target: { tabId },
            code: wrappedCode
          });
        } catch (e) {
          console.error('Script injection failed:', e);
        }
      }
    }
  } catch (error) {
    console.error('Injection error:', error);
  }
});

// 点击扩展图标打开选项页
chrome.action.onClicked.addListener(() => {
  chrome.runtime.openOptionsPage();
});

// 简单的 URL 模式匹配
function matchesPattern(url, pattern) {
  if (!pattern) return false;
  
  // 支持通配符 * 和精确匹配
  const regex = new RegExp(
    '^' + pattern
      .replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // 转义特殊字符
      .replace(/\\\*/g, '.*') // 将 \* 替换为 .*
    + '$'
  );
  
  return regex.test(url);
}
