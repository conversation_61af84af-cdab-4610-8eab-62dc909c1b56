<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>测试页面 - Custom CSS/JS Injector</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 40px;
      line-height: 1.6;
    }
    .test-box {
      background: #f0f0f0;
      padding: 20px;
      border: 1px solid #ccc;
      margin: 20px 0;
      border-radius: 5px;
    }
    .original-style {
      color: #333;
      background: #fff;
      padding: 10px;
      border: 1px solid #ddd;
    }
  </style>
</head>
<body>
  <h1>Custom CSS/JS Injector 测试页面</h1>
  
  <div class="test-box">
    <h2>测试说明</h2>
    <p>这是一个用于测试扩展功能的页面。你可以：</p>
    <ol>
      <li>在扩展选项中添加规则，URL 模式设为：<code>file://*test.html</code></li>
      <li>添加 CSS 测试代码</li>
      <li>添加 JavaScript 测试代码</li>
      <li>保存后刷新此页面查看效果</li>
    </ol>
  </div>
  
  <div class="test-box">
    <h2>CSS 测试示例</h2>
    <div class="original-style">
      这个元素有原始样式，可以通过 CSS 注入来修改
    </div>
    <p>建议的测试 CSS：</p>
    <pre><code>/* 修改背景色 */
body { background: linear-gradient(45deg, #ff6b6b, #4ecdc4) !important; }

/* 修改测试元素样式 */
.original-style {
  background: #ffeb3b !important;
  color: #333 !important;
  font-weight: bold !important;
  transform: rotate(2deg) !important;
}</code></pre>
  </div>
  
  <div class="test-box">
    <h2>JavaScript 测试示例</h2>
    <div id="js-target">这里将被 JavaScript 修改</div>
    <p>建议的测试 JavaScript：</p>
    <pre><code>// 修改元素内容
document.getElementById('js-target').innerHTML = 
  '✅ JavaScript 注入成功！时间：' + new Date().toLocaleTimeString();

// 添加新元素
const newDiv = document.createElement('div');
newDiv.style.cssText = `
  background: #4caf50;
  color: white;
  padding: 10px;
  margin: 10px 0;
  border-radius: 5px;
  font-weight: bold;
`;
newDiv.textContent = '🎉 这是通过 JavaScript 动态添加的元素';
document.body.appendChild(newDiv);

// 控制台输出
console.log('Custom script executed successfully!');</code></pre>
  </div>
  
  <div class="test-box">
    <h2>当前页面信息</h2>
    <p><strong>URL:</strong> <span id="current-url"></span></p>
    <p><strong>标题:</strong> <span id="current-title"></span></p>
    <p><strong>时间:</strong> <span id="current-time"></span></p>
  </div>
  
  <script>
    // 显示页面信息
    document.getElementById('current-url').textContent = window.location.href;
    document.getElementById('current-title').textContent = document.title;
    document.getElementById('current-time').textContent = new Date().toLocaleString();
    
    // 每秒更新时间
    setInterval(() => {
      document.getElementById('current-time').textContent = new Date().toLocaleString();
    }, 1000);
  </script>
</body>
</html>
