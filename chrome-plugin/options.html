<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Custom CSS/JS Injector - Options</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      margin-bottom: 20px;
    }
    .rule {
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 15px;
      margin-bottom: 15px;
      background: #fafafa;
    }
    .rule-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;
    }
    .rule-header input[type="checkbox"] {
      transform: scale(1.2);
    }
    .rule-header input[type="text"] {
      flex: 1;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
    }
    .rule-header button {
      background: #dc3545;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
    .rule-header button:hover {
      background: #c82333;
    }
    textarea {
      width: 100%;
      min-height: 80px;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      resize: vertical;
      box-sizing: border-box;
    }
    .textarea-label {
      font-weight: bold;
      margin: 8px 0 4px 0;
      color: #555;
    }
    .add-rule {
      background: #28a745;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin-bottom: 20px;
    }
    .add-rule:hover {
      background: #218838;
    }
    .save-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin-right: 10px;
    }
    .save-btn:hover {
      background: #0056b3;
    }
    .help {
      background: #e9ecef;
      padding: 15px;
      border-radius: 6px;
      margin-top: 20px;
      font-size: 13px;
      line-height: 1.5;
    }
    .help h3 {
      margin-top: 0;
      color: #495057;
    }
    .help code {
      background: #f8f9fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Custom CSS/JS Injector</h1>
    
    <button class="add-rule" id="addRule">+ 添加新规则</button>
    
    <div id="rules"></div>
    
    <button class="save-btn" id="save">保存配置</button>
    <span id="status"></span>
    
    <div class="help">
      <h3>使用说明</h3>
      <p><strong>URL 模式：</strong>支持通配符 <code>*</code>，例如：</p>
      <ul>
        <li><code>https://example.com/*</code> - 匹配 example.com 下所有页面</li>
        <li><code>*://github.com/*/issues</code> - 匹配 GitHub 的 issues 页面</li>
        <li><code>https://www.google.com/search*</code> - 匹配 Google 搜索页面</li>
      </ul>
      <p><strong>CSS：</strong>标准 CSS 语法，建议使用 <code>!important</code> 确保优先级</p>
      <p><strong>JavaScript：</strong>在页面上下文中执行，可访问页面的 DOM 和变量</p>
    </div>
  </div>
  
  <script src="options.js"></script>
</body>
</html>
