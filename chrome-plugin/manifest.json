{"manifest_version": 3, "name": "Custom CSS/JS Injector", "version": "0.2.1", "description": "User-defined CSS and JS injection for specified sites. Default injects nothing until configured.", "action": {"default_title": "Open options"}, "permissions": ["storage", "scripting"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "options_page": "options.html", "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}}