// 视口矩阵限制算法实现

// 基础类型定义
export type Matrix = [number, number, number, number, number, number]; // [a, b, c, d, tx, ty]
export type Point = { x: number; y: number };
export type Rect = { x: number; y: number; width: number; height: number };

// 视口信息
export interface ViewportInfo {
  width: number;
  height: number;
  matrix: Matrix;
  contentRect: Rect;
}

// 约束信息
export interface ViewportConstraints {
  minScale: number;
  maxScale: number;
  keepVisibleProportion: number; // 1=全部可见, 0=始终有内容, <0=无限制
}

// 动画接口
export interface Animation {
  tick(deltaTime: number): boolean; // 返回false表示动画结束
}

// 矩阵工具函数
export const MatrixUtils = {
  // 创建单位矩阵
  identity(): Matrix {
    return [1, 0, 0, 1, 0, 0];
  },

  // 矩阵乘法
  multiply(m1: Matrix, m2: Matrix): Matrix {
    const [a1, b1, c1, d1, tx1, ty1] = m1;
    const [a2, b2, c2, d2, tx2, ty2] = m2;
    return [
      a1 * a2 + b1 * c2,
      a1 * b2 + b1 * d2,
      c1 * a2 + d1 * c2,
      c1 * b2 + d1 * d2,
      tx1 * a2 + ty1 * c2 + tx2,
      tx1 * b2 + ty1 * d2 + ty2
    ];
  },

  // 平移变换
  translate(tx: number, ty: number): Matrix {
    return [1, 0, 0, 1, tx, ty];
  },

  // 缩放变换
  scale(sx: number, sy: number = sx): Matrix {
    return [sx, 0, 0, sy, 0, 0];
  },

  // 旋转变换
  rotate(angle: number): Matrix {
    const cos = Math.cos(angle);
    const sin = Math.sin(angle);
    return [cos, sin, -sin, cos, 0, 0];
  },

  // 获取矩阵的缩放值
  getScale(matrix: Matrix): { x: number; y: number } {
    const [a, b, c, d] = matrix;
    return {
      x: Math.sqrt(a * a + b * b),
      y: Math.sqrt(c * c + d * d)
    };
  },

  // 获取矩阵的平移值
  getTranslation(matrix: Matrix): Point {
    return { x: matrix[4], y: matrix[5] };
  },

  // 获取矩阵的旋转角度
  getRotation(matrix: Matrix): number {
    const [a, b] = matrix;
    return Math.atan2(b, a);
  },

  // 变换点
  transformPoint(point: Point, matrix: Matrix): Point {
    const [a, b, c, d, tx, ty] = matrix;
    return {
      x: a * point.x + c * point.y + tx,
      y: b * point.x + d * point.y + ty
    };
  },

  // 变换矩形
  transformRect(rect: Rect, matrix: Matrix): Rect {
    const corners = [
      { x: rect.x, y: rect.y },
      { x: rect.x + rect.width, y: rect.y },
      { x: rect.x + rect.width, y: rect.y + rect.height },
      { x: rect.x, y: rect.y + rect.height }
    ];

    const transformedCorners = corners.map(corner =>
      MatrixUtils.transformPoint(corner, matrix)
    );

    const xs = transformedCorners.map(p => p.x);
    const ys = transformedCorners.map(p => p.y);

    const minX = Math.min(...xs);
    const minY = Math.min(...ys);
    const maxX = Math.max(...xs);
    const maxY = Math.max(...ys);

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  },

  // 矩阵求逆
  invert(matrix: Matrix): Matrix | null {
    const [a, b, c, d, tx, ty] = matrix;
    const det = a * d - b * c;

    if (Math.abs(det) < 1e-10) {
      return null; // 矩阵不可逆
    }

    const invDet = 1 / det;
    return [
      d * invDet,
      -b * invDet,
      -c * invDet,
      a * invDet,
      (c * ty - d * tx) * invDet,
      (b * tx - a * ty) * invDet
    ];
  }
};

// 几何工具函数
export const GeometryUtils = {
  // 计算两个矩形的交集
  intersectRect(rect1: Rect, rect2: Rect): Rect | null {
    const left = Math.max(rect1.x, rect2.x);
    const top = Math.max(rect1.y, rect2.y);
    const right = Math.min(rect1.x + rect1.width, rect2.x + rect2.width);
    const bottom = Math.min(rect1.y + rect1.height, rect2.y + rect2.height);

    if (left >= right || top >= bottom) {
      return null; // 无交集
    }

    return {
      x: left,
      y: top,
      width: right - left,
      height: bottom - top
    };
  },

  // 计算矩形面积
  rectArea(rect: Rect): number {
    return rect.width * rect.height;
  },

  // 计算两个矩形的重叠比例
  getOverlapRatio(rect1: Rect, rect2: Rect): number {
    const intersection = GeometryUtils.intersectRect(rect1, rect2);
    if (!intersection) return 0;

    const intersectionArea = GeometryUtils.rectArea(intersection);
    const rect1Area = GeometryUtils.rectArea(rect1);

    return intersectionArea / rect1Area;
  },

  // 检查矩形是否完全包含另一个矩形
  containsRect(container: Rect, contained: Rect): boolean {
    return contained.x >= container.x &&
           contained.y >= container.y &&
           contained.x + contained.width <= container.x + container.width &&
           contained.y + contained.height <= container.y + container.height;
  },

  // 计算点到矩形的最短距离
  pointToRectDistance(point: Point, rect: Rect): number {
    const dx = Math.max(0, Math.max(rect.x - point.x, point.x - (rect.x + rect.width)));
    const dy = Math.max(0, Math.max(rect.y - point.y, point.y - (rect.y + rect.height)));
    return Math.sqrt(dx * dx + dy * dy);
  }
};

// 视口矩阵限制算法
export class ViewportMatrixConstrainer {
  private viewport: ViewportInfo;
  private constraints: ViewportConstraints;

  constructor(viewport: ViewportInfo, constraints: ViewportConstraints) {
    this.viewport = viewport;
    this.constraints = constraints;
  }

  // 检查并修正矩阵
  constrainMatrix(matrix: Matrix): {
    constrainedMatrix: Matrix;
    needsAnimation: boolean;
    animation?: Animation;
  } {
    // 如果keepVisibleProportion < 0，表示无限制
    if (this.constraints.keepVisibleProportion < 0) {
      return { constrainedMatrix: matrix, needsAnimation: false };
    }

    let constrainedMatrix = [...matrix] as Matrix;
    let hasChanges = false;

    // 1. 限制缩放
    const scale = MatrixUtils.getScale(constrainedMatrix);
    const clampedScaleX = Math.max(this.constraints.minScale,
                                  Math.min(this.constraints.maxScale, scale.x));
    const clampedScaleY = Math.max(this.constraints.minScale,
                                  Math.min(this.constraints.maxScale, scale.y));

    if (Math.abs(scale.x - clampedScaleX) > 1e-6 || Math.abs(scale.y - clampedScaleY) > 1e-6) {
      // 重新构建矩阵，保持旋转和平移
      const rotation = MatrixUtils.getRotation(constrainedMatrix);
      const translation = MatrixUtils.getTranslation(constrainedMatrix);

      const scaleMatrix = MatrixUtils.scale(clampedScaleX, clampedScaleY);
      const rotateMatrix = MatrixUtils.rotate(rotation);
      const translateMatrix = MatrixUtils.translate(translation.x, translation.y);

      constrainedMatrix = MatrixUtils.multiply(
        MatrixUtils.multiply(scaleMatrix, rotateMatrix),
        translateMatrix
      );
      hasChanges = true;
    }

    // 2. 限制平移以保持可见内容
    const translationConstraint = this.calculateTranslationConstraints(constrainedMatrix);
    if (translationConstraint) {
      const currentTranslation = MatrixUtils.getTranslation(constrainedMatrix);
      const constrainedTranslation = {
        x: Math.max(translationConstraint.minX,
            Math.min(translationConstraint.maxX, currentTranslation.x)),
        y: Math.max(translationConstraint.minY,
            Math.min(translationConstraint.maxY, currentTranslation.y))
      };

      if (Math.abs(currentTranslation.x - constrainedTranslation.x) > 1e-6 ||
          Math.abs(currentTranslation.y - constrainedTranslation.y) > 1e-6) {
        constrainedMatrix[4] = constrainedTranslation.x;
        constrainedMatrix[5] = constrainedTranslation.y;
        hasChanges = true;
      }
    }

    // 3. 创建动画
    if (hasChanges) {
      const animation = this.createBounceAnimation(matrix, constrainedMatrix);
      return {
        constrainedMatrix,
        needsAnimation: true,
        animation
      };
    }

    return { constrainedMatrix, needsAnimation: false };
  }

  // 计算平移约束
  private calculateTranslationConstraints(matrix: Matrix): {
    minX: number; maxX: number; minY: number; maxY: number;
  } | null {
    // 计算变换后的内容矩形
    const transformedContent = MatrixUtils.transformRect(this.viewport.contentRect, matrix);

    // 视口矩形
    const viewportRect: Rect = {
      x: 0,
      y: 0,
      width: this.viewport.width,
      height: this.viewport.height
    };

    // 根据keepVisibleProportion计算约束
    if (this.constraints.keepVisibleProportion === 1) {
      // 内容必须完全在视口内
      return {
        minX: -transformedContent.x,
        maxX: this.viewport.width - (transformedContent.x + transformedContent.width),
        minY: -transformedContent.y,
        maxY: this.viewport.height - (transformedContent.y + transformedContent.height)
      };
    } else if (this.constraints.keepVisibleProportion === 0) {
      // 视口内始终有内容
      return {
        minX: this.viewport.width - transformedContent.x,
        maxX: -transformedContent.x - transformedContent.width,
        minY: this.viewport.height - transformedContent.y,
        maxY: -transformedContent.y - transformedContent.height
      };
    } else {
      // 按比例保持可见
      const requiredVisibleArea = GeometryUtils.rectArea(transformedContent) * this.constraints.keepVisibleProportion;

      // 计算允许的偏移范围
      const maxOffsetX = transformedContent.width * (1 - this.constraints.keepVisibleProportion);
      const maxOffsetY = transformedContent.height * (1 - this.constraints.keepVisibleProportion);

      return {
        minX: -transformedContent.x - maxOffsetX,
        maxX: this.viewport.width - transformedContent.x + maxOffsetX,
        minY: -transformedContent.y - maxOffsetY,
        maxY: this.viewport.height - transformedContent.y + maxOffsetY
      };
    }
  }

  // 创建反弹动画
  private createBounceAnimation(fromMatrix: Matrix, toMatrix: Matrix): Animation {
    return new BounceAnimation(fromMatrix, toMatrix);
  }

  // 更新视口信息
  updateViewport(viewport: ViewportInfo): void {
    this.viewport = viewport;
  }

  // 更新约束信息
  updateConstraints(constraints: ViewportConstraints): void {
    this.constraints = constraints;
  }
}

// 反弹动画实现
export class BounceAnimation implements Animation {
  private duration: number;
  private fromMatrix: Matrix;
  private toMatrix: Matrix;
  private currentTime: number = 0;

  constructor(fromMatrix: Matrix, toMatrix: Matrix, duration: number = 300) {
    this.fromMatrix = [...fromMatrix] as Matrix;
    this.toMatrix = [...toMatrix] as Matrix;
    this.duration = duration;
  }

  tick(deltaTime: number): boolean {
    this.currentTime += deltaTime;
    const progress = Math.min(this.currentTime / this.duration, 1);

    if (progress >= 1) {
      return false; // 动画结束
    }

    return true; // 动画继续
  }

  // 获取当前插值矩阵
  getCurrentMatrix(): Matrix {
    const progress = Math.min(this.currentTime / this.duration, 1);
    const easedProgress = this.easeOutBounce(progress);

    return this.interpolateMatrix(this.fromMatrix, this.toMatrix, easedProgress);
  }

  // 反弹缓动函数
  private easeOutBounce(t: number): number {
    if (t < 1 / 2.75) {
      return 7.5625 * t * t;
    } else if (t < 2 / 2.75) {
      return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
    } else if (t < 2.5 / 2.75) {
      return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
    } else {
      return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
    }
  }

  // 矩阵插值
  private interpolateMatrix(from: Matrix, to: Matrix, t: number): Matrix {
    return [
      from[0] + (to[0] - from[0]) * t,
      from[1] + (to[1] - from[1]) * t,
      from[2] + (to[2] - from[2]) * t,
      from[3] + (to[3] - from[3]) * t,
      from[4] + (to[4] - from[4]) * t,
      from[5] + (to[5] - from[5]) * t
    ];
  }
}

// 便捷函数
export function constrainViewportMatrix(
  viewport: ViewportInfo,
  constraints: ViewportConstraints
): {
  constrainedMatrix: Matrix;
  needsAnimation: boolean;
  animation?: Animation;
} {
  const constrainer = new ViewportMatrixConstrainer(viewport, constraints);
  return constrainer.constrainMatrix(viewport.matrix);
}

// 创建默认约束
export function createDefaultConstraints(): ViewportConstraints {
  return {
    minScale: 0.1,
    maxScale: 10,
    keepVisibleProportion: 0.3 // 保持30%内容可见
  };
}

// 检查矩阵是否需要约束
export function needsConstraining(
  viewport: ViewportInfo,
  constraints: ViewportConstraints
): boolean {
  const result = constrainViewportMatrix(viewport, constraints);
  return result.needsAnimation;
}

// 限制视口矩阵 - 直接返回限制后的矩阵
export function limitViewportMatrix(
  viewport: ViewportInfo,
  constraints: ViewportConstraints
): Matrix {
  // 如果keepVisibleProportion < 0，表示无限制
  if (constraints.keepVisibleProportion < 0) {
    return viewport.matrix;
  }

  let constrainedMatrix = [...viewport.matrix] as Matrix;

  // 1. 限制缩放
  const scale = MatrixUtils.getScale(constrainedMatrix);
  const clampedScaleX = Math.max(constraints.minScale,
                                Math.min(constraints.maxScale, scale.x));
  const clampedScaleY = Math.max(constraints.minScale,
                                Math.min(constraints.maxScale, scale.y));

  if (Math.abs(scale.x - clampedScaleX) > 1e-6 || Math.abs(scale.y - clampedScaleY) > 1e-6) {
    // 重新构建矩阵，保持旋转和平移
    const rotation = MatrixUtils.getRotation(constrainedMatrix);
    const translation = MatrixUtils.getTranslation(constrainedMatrix);

    const scaleMatrix = MatrixUtils.scale(clampedScaleX, clampedScaleY);
    const rotateMatrix = MatrixUtils.rotate(rotation);
    const translateMatrix = MatrixUtils.translate(translation.x, translation.y);

    constrainedMatrix = MatrixUtils.multiply(
      MatrixUtils.multiply(scaleMatrix, rotateMatrix),
      translateMatrix
    );
  }

  // 2. 限制平移以保持可见内容
  const translationConstraint = calculateTranslationLimits(viewport, constrainedMatrix, constraints);
  if (translationConstraint) {
    const currentTranslation = MatrixUtils.getTranslation(constrainedMatrix);
    const constrainedTranslation = {
      x: Math.max(translationConstraint.minX,
          Math.min(translationConstraint.maxX, currentTranslation.x)),
      y: Math.max(translationConstraint.minY,
          Math.min(translationConstraint.maxY, currentTranslation.y))
    };

    constrainedMatrix[4] = constrainedTranslation.x;
    constrainedMatrix[5] = constrainedTranslation.y;
  }

  return constrainedMatrix;
}

// 计算平移限制的辅助函数
function calculateTranslationLimits(
  viewport: ViewportInfo,
  matrix: Matrix,
  constraints: ViewportConstraints
): { minX: number; maxX: number; minY: number; maxY: number } | null {
  // 计算变换后的内容矩形
  const transformedContent = MatrixUtils.transformRect(viewport.contentRect, matrix);

  // 根据keepVisibleProportion计算约束
  if (constraints.keepVisibleProportion === 1) {
    // 内容必须完全在视口内
    return {
      minX: -transformedContent.x,
      maxX: viewport.width - (transformedContent.x + transformedContent.width),
      minY: -transformedContent.y,
      maxY: viewport.height - (transformedContent.y + transformedContent.height)
    };
  } else if (constraints.keepVisibleProportion === 0) {
    // 视口内始终有内容
    return {
      minX: viewport.width - transformedContent.x,
      maxX: -transformedContent.x - transformedContent.width,
      minY: viewport.height - transformedContent.y,
      maxY: -transformedContent.y - transformedContent.height
    };
  } else {
    // 按比例保持可见
    const maxOffsetX = transformedContent.width * (1 - constraints.keepVisibleProportion);
    const maxOffsetY = transformedContent.height * (1 - constraints.keepVisibleProportion);

    return {
      minX: -transformedContent.x - maxOffsetX,
      maxX: viewport.width - transformedContent.x + maxOffsetX,
      minY: -transformedContent.y - maxOffsetY,
      maxY: viewport.height - transformedContent.y + maxOffsetY
    };
  }
}

// 使用示例和文档
/*
使用示例:

// 1. 基本使用
const viewport: ViewportInfo = {
  width: 800,
  height: 600,
  matrix: [1, 0, 0, 1, 100, 50], // 当前变换矩阵
  contentRect: { x: 0, y: 0, width: 1000, height: 800 } // 内容区域
};

const constraints: ViewportConstraints = {
  minScale: 0.1,
  maxScale: 5,
  keepVisibleProportion: 0.5 // 保持50%内容可见
};

const result = constrainViewportMatrix(viewport, constraints);
if (result.needsAnimation && result.animation) {
  // 启动动画
  const animate = () => {
    const shouldContinue = result.animation!.tick(16); // 16ms per frame
    if (shouldContinue) {
      const currentMatrix = (result.animation as BounceAnimation).getCurrentMatrix();
      // 应用当前矩阵到视口
      applyMatrixToViewport(currentMatrix);
      requestAnimationFrame(animate);
    } else {
      // 动画结束，应用最终矩阵
      applyMatrixToViewport(result.constrainedMatrix);
    }
  };
  animate();
} else {
  // 直接应用约束后的矩阵
  applyMatrixToViewport(result.constrainedMatrix);
}

// 2. 不同的keepVisibleProportion值的含义:
// - 1.0: 内容必须完全在视口内
// - 0.5: 至少50%的内容在视口内
// - 0.0: 视口内始终有内容即可
// - -1: 无偏移限制

// 3. 动态更新约束
const constrainer = new ViewportMatrixConstrainer(viewport, constraints);
// 更新视口大小
constrainer.updateViewport({ ...viewport, width: 1024, height: 768 });
// 更新约束
constrainer.updateConstraints({ ...constraints, maxScale: 8 });

// 4. 矩阵工具函数使用
const matrix = MatrixUtils.multiply(
  MatrixUtils.scale(2, 2),
  MatrixUtils.translate(100, 50)
);
const transformedPoint = MatrixUtils.transformPoint({ x: 10, y: 20 }, matrix);
const scale = MatrixUtils.getScale(matrix);
const rotation = MatrixUtils.getRotation(matrix);
*/

