export class RenderSystem {
    private canvas: HTMLCanvasElement;
    private ctx: CanvasRenderingContext2D;
    private rootComponent?: UIComponent;
    private frameId?: number;
    private lastUpdateTime: number = 0;
    private isRunning: boolean = false;

    constructor(canvas: HTMLCanvasElement) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d')!;
        this.setupCanvas();
    }

    private setupCanvas(): void {
        // Set up canvas properties
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;

        // Add resize listener
        window.addEventListener('resize', () => {
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;
            this.render();
        });
    }

    setRootComponent(component: UIComponent): void {
        this.rootComponent = component;
        this.startRendering();
    }

    private startRendering(): void {
        if (this.isRunning) return;
        this.isRunning = true;
        this.renderLoop();
    }

    private renderLoop(): void {
        if (!this.isRunning) return;

        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastUpdateTime;
        this.lastUpdateTime = currentTime;

        if (this.rootComponent) {
            this.renderComponent(this.rootComponent, deltaTime);
        }

        this.frameId = requestAnimationFrame(() => this.renderLoop());
    }

    private renderComponent(component: UIComponent, deltaTime: number): void {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Update and draw component
        component.update(deltaTime);
        component.draw(this.ctx);

        // Render children
        component.children.forEach(child => {
            this.renderComponent(child, deltaTime);
        });
    }

    stopRendering(): void {
        if (this.frameId) {
            cancelAnimationFrame(this.frameId);
            this.frameId = undefined;
            this.isRunning = false;
        }
    }

    dispose(): void {
        this.stopRendering();
        this.rootComponent = undefined;
    }

    // Utility methods for rendering
    private drawRectangle(
        x: number,
        y: number,
        width: number,
        height: number,
        color: string,
        borderColor?: string,
        borderWidth?: number,
        borderRadius?: number
    ): void {
        this.ctx.save();
        this.ctx.fillStyle = color;
        if (borderColor) {
            this.ctx.strokeStyle = borderColor;
            this.ctx.lineWidth = borderWidth || 1;
        }

        if (borderRadius) {
            this.drawRoundedRect(x, y, width, height, borderRadius);
        } else {
            this.ctx.fillRect(x, y, width, height);
            if (borderColor) {
                this.ctx.strokeRect(x, y, width, height);
            }
        }

        this.ctx.restore();
    }

    private drawRoundedRect(
        x: number,
        y: number,
        width: number,
        height: number,
        radius: number
    ): void {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();

        this.ctx.fill();
        this.ctx.stroke();
    }

    private drawCircle(
        x: number,
        y: number,
        radius: number,
        color: string,
        borderColor?: string,
        borderWidth?: number
    ): void {
        this.ctx.save();
        this.ctx.fillStyle = color;
        if (borderColor) {
            this.ctx.strokeStyle = borderColor;
            this.ctx.lineWidth = borderWidth || 1;
        }

        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.fill();
        if (borderColor) {
            this.ctx.stroke();
        }

        this.ctx.restore();
    }

    private drawText(
        x: number,
        y: number,
        text: string,
        fontSize: number,
        fontFamily: string,
        color: string,
        textAlign: CanvasTextAlign,
        textBaseline: CanvasTextBaseline
    ): void {
        this.ctx.save();
        this.ctx.font = `${fontSize}px ${fontFamily}`;
        this.ctx.fillStyle = color;
        this.ctx.textAlign = textAlign;
        this.ctx.textBaseline = textBaseline;

        this.ctx.fillText(text, x, y);
        this.ctx.restore();
    }

    // Add more utility methods for rendering as needed
}
