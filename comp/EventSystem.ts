export class EventSystem {
    private canvas: HTMLCanvasElement;
    private eventListeners: Map<string, ((event: Event) => void)[]> = new Map();

    constructor(canvas: HTMLCanvasElement) {
        this.canvas = canvas;
        this.initializeEventListeners();
    }

    private initializeEventListeners(): void {
        const eventTypes = [
            'click', 'mousedown', 'mouseup', 'mousemove',
            'mouseenter', 'mouseleave', 'keydown', 'keyup'
        ];

        eventTypes.forEach((eventType) => {
            this.canvas.addEventListener(eventType, (event) => {
                this.handleEvent(event);
            });
        });
    }

    private handleEvent(event: Event): void {
        // Get the position of the event
        const rect = this.canvas.getBoundingClientRect();
        const x = (event as MouseEvent).clientX - rect.left;
        const y = (event as MouseEvent).clientY - rect.top;

        // Check if the event is within any component
        // This is a placeholder, actual implementation depends on the component hierarchy
        const targetComponent = this.findTargetComponent(x, y);

        if (targetComponent) {
            // Trigger event on the target component
            targetComponent.handleEvent(event);
        }
    }

    private findTargetComponent(x: number, y: number): UIComponent | undefined {
        // This is a placeholder, actual implementation should traverse the component tree
        // and find the component at the given coordinates
        return undefined;
    }

    addEventListener(
        component: UIComponent,
        eventType: keyof UIComponentEvents,
        listener: (event: Event) => void
    ): void {
        const listeners = this.eventListeners.get(eventType) || [];
        listeners.push(listener);
        this.eventListeners.set(eventType, listeners);

        // Add event listener to the component
        component.events[eventType] = listener;
    }

    removeEventListener(
        component: UIComponent,
        eventType: keyof UIComponentEvents,
        listener: (event: Event) => void
    ): void {
        const listeners = this.eventListeners.get(eventType);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }

        // Remove event listener from the component
        component.events[eventType] = undefined;
    }

    dispose(): void {
        // Remove all event listeners
        this.eventListeners.clear();
    }
}
