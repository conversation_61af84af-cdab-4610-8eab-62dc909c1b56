import { UIComponent, UIComponentProps, UIComponentEvents } from './UIComponent';

export interface LeafProps extends UIComponentProps {
    type: 'rect' | 'circle' | 'text' | 'image';
    color?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    text?: string;
    fontSize?: number;
    fontFamily?: string;
    textAlign?: CanvasTextAlign;
    textBaseline?: CanvasTextBaseline;
    src?: string; // For image type
}

export class Leaf extends UIComponent {
    private type: 'rect' | 'circle' | 'text' | 'image';
    private color: string;
    private borderColor: string;
    private borderWidth: number;
    private borderRadius: number;
    private text: string;
    private fontSize: number;
    private fontFamily: string;
    private textAlign: CanvasTextAlign;
    private textBaseline: CanvasTextBaseline;
    private src: string; // For image type
    private image?: HTMLImageElement;

    constructor(props: LeafProps, events?: UIComponentEvents) {
        super(props, events);
        this.type = props.type;
        this.color = props.color || '#000000';
        this.borderColor = props.borderColor || '#000000';
        this.borderWidth = props.borderWidth || 0;
        this.borderRadius = props.borderRadius || 0;
        this.text = props.text || '';
        this.fontSize = props.fontSize || 12;
        this.fontFamily = props.fontFamily || 'Arial';
        this.textAlign = props.textAlign || 'start';
        this.textBaseline = props.textBaseline || 'alphabetic';
        this.src = props.src || '';

        if (this.type === 'image' && this.src) {
            this.loadImage();
        }
    }

    draw(ctx: CanvasRenderingContext2D): void {
        ctx.save();
        ctx.translate(this.props.x, this.props.y);

        // Draw background
        if (this.type !== 'image') {
            ctx.fillStyle = this.color;
            ctx.strokeStyle = this.borderColor;
            ctx.lineWidth = this.borderWidth;

            switch (this.type) {
                case 'rect':
                    this.drawRectangle(ctx);
                    break;
                case 'circle':
                    this.drawCircle(ctx);
                    break;
                case 'text':
                    this.drawText(ctx);
                    break;
            }
        }

        // Draw image if type is image
        if (this.type === 'image' && this.image) {
            ctx.drawImage(this.image, 0, 0, this.props.width, this.props.height);
        }

        ctx.restore();
    }

    private drawRectangle(ctx: CanvasRenderingContext2D): void {
        if (this.borderRadius > 0) {
            // Draw rounded rectangle
            this.drawRoundedRect(ctx);
        } else {
            // Draw regular rectangle
            ctx.fillRect(0, 0, this.props.width, this.props.height);
            if (this.borderWidth > 0) {
                ctx.strokeRect(0, 0, this.props.width, this.props.height);
            }
        }
    }

    private drawRoundedRect(ctx: CanvasRenderingContext2D): void {
        const radius = this.borderRadius;
        const width = this.props.width;
        const height = this.props.height;

        ctx.beginPath();
        ctx.moveTo(radius, 0);
        ctx.lineTo(width - radius, 0);
        ctx.quadraticCurveTo(width, 0, width, radius);
        ctx.lineTo(width, height - radius);
        ctx.quadraticCurveTo(width, height, width - radius, height);
        ctx.lineTo(radius, height);
        ctx.quadraticCurveTo(0, height, 0, height - radius);
        ctx.lineTo(0, radius);
        ctx.quadraticCurveTo(0, 0, radius, 0);
        ctx.closePath();

        ctx.fill();
        if (this.borderWidth > 0) {
            ctx.stroke();
        }
    }

    private drawCircle(ctx: CanvasRenderingContext2D): void {
        const centerX = this.props.width / 2;
        const centerY = this.props.height / 2;
        const radius = Math.min(this.props.width, this.props.height) / 2;

        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.fill();
        if (this.borderWidth > 0) {
            ctx.stroke();
        }
    }

    private drawText(ctx: CanvasRenderingContext2D): void {
        ctx.font = `${this.fontSize}px ${this.fontFamily}`;
        ctx.textAlign = this.textAlign;
        ctx.textBaseline = this.textBaseline;
        ctx.fillStyle = this.color;

        const x = this.props.width / 2;
        const y = this.props.height / 2;
        ctx.fillText(this.text, x, y);
    }

    private loadImage(): void {
        this.image = new Image();
        this.image.src = this.src;
        this.image.onload = () => {
            // Trigger redraw if needed
        };
        this.image.onerror = (error) => {
            console.error('Image loading error:', error);
        };
    }
}
