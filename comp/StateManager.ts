export type State<T> = {
    value: T;
    listeners: ((newValue: T) => void)[];
    update: (newValue: T) => void;
    subscribe: (listener: (newValue: T) => void) => () => void;
}

export class StateManager {
    private states: Map<string, State<any>> = new Map();

    createState<T>(key: string, initialValue: T): State<T> {
        if (this.states.has(key)) {
            throw new Error(`State with key "${key}" already exists`);
        }

        const state: State<T> = {
            value: initialValue,
            listeners: [],
            update: (newValue: T) => {
                state.value = newValue;
                state.listeners.forEach(listener => listener(newValue));
            },
            subscribe: (listener: (newValue: T) => void) => {
                state.listeners.push(listener);
                return () => {
                    const index = state.listeners.indexOf(listener);
                    if (index !== -1) {
                        state.listeners.splice(index, 1);
                    }
                };
            }
        };

        this.states.set(key, state);
        return state;
    }

    getState<T>(key: string): State<T> | undefined {
        return this.states.get(key) as State<T> | undefined;
    }

    setState<T>(key: string, newValue: T): void {
        const state = this.states.get(key);
        if (state) {
            state.update(newValue);
        }
    }

    dispose(): void {
        this.states.clear();
    }
}
