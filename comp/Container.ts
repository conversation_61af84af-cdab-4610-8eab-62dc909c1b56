import { UIComponent, UIComponentProps, UIComponentEvents } from './UIComponent';

export interface ContainerProps extends UIComponentProps {
    layout?: 'horizontal' | 'vertical' | 'grid';
    padding?: number;
    spacing?: number;
    alignItems?: 'start' | 'center' | 'end';
    justifyContent?: 'start' | 'center' | 'end';
}

export class Container extends UIComponent {
    private layout: 'horizontal' | 'vertical' | 'grid';
    private padding: number;
    private spacing: number;
    private alignItems: 'start' | 'center' | 'end';
    private justifyContent: 'start' | 'center' | 'end';

    constructor(props: ContainerProps, events?: UIComponentEvents) {
        super(props, events);
        this.layout = props.layout || 'vertical';
        this.padding = props.padding || 0;
        this.spacing = props.spacing || 0;
        this.alignItems = props.alignItems || 'start';
        this.justifyContent = props.justifyContent || 'start';
    }

    draw(ctx: CanvasRenderingContext2D): void {
        // Draw container background
        ctx.save();
        ctx.fillStyle = this.props.style?.backgroundColor || 'transparent';
        ctx.fillRect(
            this.props.x,
            this.props.y,
            this.props.width,
            this.props.height
        );

        // Layout children
        this.layoutChildren(ctx);

        ctx.restore();
    }

    private layoutChildren(ctx: CanvasRenderingContext2D): void {
        let currentX = this.padding;
        let currentY = this.padding;
        const childCount = this.children.length;

        this.children.forEach((child, index) => {
            if (!child.props.visible) return;

            const childWidth = child.props.width;
            const childHeight = child.props.height;

            // Calculate child position based on layout
            switch (this.layout) {
                case 'horizontal':
                    child.props.x = currentX;
                    child.props.y = currentY;
                    currentX += childWidth + this.spacing;
                    break;
                case 'vertical':
                    child.props.x = currentX;
                    child.props.y = currentY;
                    currentY += childHeight + this.spacing;
                    break;
                case 'grid':
                    // Implement grid layout logic
                    break;
            }

            // Draw child
            child.draw(ctx);
        });

        // Adjust container size if needed
        this.adjustSize();
    }

    private adjustSize(): void {
        // Adjust container size based on children
        // This method can be called after layout to update container size
    }

    addChild(component: UIComponent): void {
        super.addChild(component);
        this.layoutChildren(this.getContext());
    }

    removeChild(component: UIComponent): void {
        super.removeChild(component);
        this.layoutChildren(this.getContext());
    }

    private getContext(): CanvasRenderingContext2D {
        // Get the canvas context
        // This is a placeholder, actual implementation depends on the canvas setup
        return {} as CanvasRenderingContext2D;
    }
}
