export const Flag_Render = 1 << 0;
// export const Flag_Layout = 0b10;//暂时不单独处理, 只要>1都重新布局一次
// export const Flag_Modifier = 0b100;//暂时不单独处理
export const Flag_Content = 1 << 1;
export const Flag_Layout = 1 << 2;
export const Flag_Child = 1 << 3;
export const Flag_All = Flag_Render | Flag_Content | Flag_Layout | Flag_Child;

export const LayoutAndContent = Flag_Layout | Flag_Content;

class UI{
    __flag = Flag_All
    parent?:UI = undefined

  invalidate(f: number) {
    const newF = this.__flag | f;
    if (this.__flag === newF) {
      return;
    }
    this.__flag = newF;
    this.parent?.onChildInvalidate(this, f);
  }
  onChildInvalidate(child: UI, f: number) {
    this.invalidate(f & LayoutAndContent ? Flag_Content : Flag_Render);
  }

  validate(flag:number){

  }
}