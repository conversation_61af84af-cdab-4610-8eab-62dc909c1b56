// const prevSibling = Symbol(), nextSibling = Symbol();


export type LinkedItem<T extends object = object> = T & WithProvNext<T>;

export interface WithProvNext<T extends object = object> {
  __prov?: LinkedItem<T>;
  __next?: LinkedItem<T>;
}

export class LinkedListBySelf<T extends object = object> implements Iterable<T> {
  getByIndex(index: number): T | undefined {
    const l = this.length;
    if (index >= l || index < 0 || !this.head) {
      return;
    }
    return (
      index < l / 2 ? getFromHead(this.head, index) : getFromTail(this.tail as T, l - index)
    ) as T;
  }
  private __head?: LinkedItem<T> = undefined;
  private __tail?: LinkedItem<T> = undefined;
  private __length = 0;

  get head() {
    return this.__head;
  }
  get tail() {
    return this.__tail;
  }
  get length() {
    return this.__length;
  }

  private __init(d: LinkedItem<T>) {
    this.__head = this.__tail = d;
    this.onAdded(d as T);
    this.__length = 1;
  }
  set(list: T[]) {
    this.clear();
    const count = list.length;
    if (!count) {
      return;
    }
    let i = 0;
    let prov: LinkedItem<T> | undefined;
    while (i < count) {
      const d: LinkedItem<T> = list[i++];
      d.__prov = prov;
      prov && (prov.__next = d);
      prov = d;
      this.onAdded(d as T);
    }
    this.__head = list[0];
    this.__tail = prov;
    this.__length = count;
  }
  push(d: LinkedItem<T>) {
    if (!this.__tail) {
      return this.__init(d);
    }
    d.__prov = this.__tail;
    this.__tail.__next = this.__tail = d;
    this.onAdded(d as T);
    this.__length++;
  }

  onAdded(t: T) {}
  onRemoved(t: T) {}

  //没有检查是否在当前list中
  insert(d: LinkedItem<T>, current?: LinkedItem<T>) {
    if (!current) {
      if (!this.__head) {
        return this.__init(d);
      }
      // 插入到头部：d -> oldHead
      d.__prov = undefined;
      d.__next = this.__head;
      this.__head.__prov = d;
      this.__head = d;
    } else {
      if (d === current) {
        return;
      }
      // 插入到 current 之后
      const next = current.__next;
      current.__next = d;
      d.__prov = current;
      d.__next = next;
      if (next) {
        next.__prov = d;
      } else {
        this.__tail = d;
      }
    }
    this.onAdded(d as T);
    this.__length++;
  }

  replace(d: LinkedItem<T>, current: LinkedItem<T>) {
    const prev = current.__prov;
    const next = current.__next;

    d.__prov = prev;
    d.__next = next;

    if (prev) {
      prev.__next = d;
    } else {
      this.__head = d;
    }

    if (next) {
      next.__prov = d;
    } else {
      this.__tail = d;
    }

    current.__prov = current.__next = undefined;
    // 长度不变，不触发 onAdded/onRemoved
  }

  move(d: LinkedItem<T>, current?: LinkedItem<T>) {
    if (d === current || d.__prov === current) {
      return false;
    }
    // 先将 d 从原位置脱链
    const prev = d.__prov;
    const next = d.__next;
    if (prev) {
      prev.__next = next;
    } else if (next) {
      // d 是头
      this.__head = next;
    }
    if (next) {
      next.__prov = prev;
    } else if (prev) {
      // d 是尾
      this.__tail = prev;
    }

    // 插入到目标位置
    if (!current) {
      // 移到最前面
      d.__prov = undefined;
      d.__next = this.__head;
      if (this.__head) {
        this.__head.__prov = d;
      }
      this.__head = d;
      if (!this.__tail) {
        this.__tail = d;
      }
    } else {
      const after = current.__next;
      current.__next = d;
      d.__prov = current;
      d.__next = after;
      if (after) {
        after.__prov = d;
      } else {
        this.__tail = d;
      }
    }
    return true;
  }

  moveLast(d: LinkedItem<T>) {
    return this.move(d, this.__tail);
  }
  moveFirst(d: LinkedItem<T>) {
    return this.move(d);
  }

  clear() {
    if (this.__head) {
      let prov = this.__head;
      this.onRemoved(prov as T);
      let current = prov.__next;
      while (current) {
        prov.__next = current.__prov = undefined;
        ((prov = current), (current = prov.__next));
        this.onRemoved(prov as T);
      }
      this.__head = this.__tail = undefined;
      this.__length = 0;
    }
  }

  remove(d: LinkedItem<T>) {
    const { __prov: prov, __next: next } = d;
    if (prov) {
      if ((prov.__next = next)) {
        next.__prov = prov;
      } else {
        this.__tail = prov;
      }
    } else if (next) {
      ((next.__prov = undefined), (this.__head = next));
    } else {
      if (d === this.__head) {
        this.clear();
        return true;
      }
      return false;
    }
    this.onRemoved(d as T);
    this.__length--;
    return true;
  }

  forEach(call: (t: T) => void) {
    this.__head && eachByLinked(this.__head, call);
  }
  some(call: (t: T, ...args: any) => boolean): boolean {
    let d: LinkedItem<T> | undefined = this.__head;
    while (d) {
      if (call(d as T)) {
        return true;
      }
      d = d.__next;
    }
    return false;
  }
  reverseSome(call: (t: T) => any) {
    let d: LinkedItem<T> | undefined = this.__tail;
    while (d) {
      if (call(d as T)) {
        return true;
      }
      d = d.__prov;
    }
    return false;
  }
  every(call: (t: T, ...args: any) => boolean): boolean {
    let d = this.__head;
    while (d) {
      if (!call(d as T)) {
        return false;
      }
      d = d.__next;
    }
    return true;
  }
  map<T2 = any>(call: (t: T) => T2) {
    return this.__head && mapByLinked(this.__head, call, this.__length);
  }

  *[Symbol.iterator]() {
    let d = this.__head;
    while (d) {
      yield d as T;
      d = d.__next;
    }
  }
  static fromArray<T extends object = object>(list: T[]) {
    const l = new LinkedListBySelf<T>();
    l.set(list);
    return l;
  }
}

function eachByLinked<T extends object = object>(root: LinkedItem<T>, call: (t: T) => void) {
  let d: LinkedItem<T> | undefined = root;
  while (d) {
    call(d as T);
    d = d.__next;
  }
}

function mapByLinked<T extends object = object, T2 = any>(
  root: LinkedItem<T>,
  call: (t: T) => T2,
  l: number,
) {
  const list: T2[] = new Array(l);
  let d: LinkedItem<T> | undefined = root;
  let i = 0;
  while (d) {
    list[i++] = call(d as T);
    d = d.__next;
  }
  return list;
}

function getFromHead<T extends object = object>(head: LinkedItem<T>, index: number) {
  let e: LinkedItem<T> | undefined = head;
  while (e && index-- > 0) {
    e = e.__next;
  }
  return e;
}
function getFromTail(tail: LinkedItem, index: number) {
  let e: LinkedItem | undefined = tail;
  while (e && --index > 0) {
    e = e.__prov;
  }
  return e;
}
