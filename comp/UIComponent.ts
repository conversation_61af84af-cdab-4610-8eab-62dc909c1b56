export interface UIComponentProps {
    id?: string;
    x: number;
    y: number;
    width: number;
    height: number;
    visible?: boolean;
    zIndex?: number;
    opacity?: number;
    className?: string;
    style?: Record<string, any>;
}

export interface UIComponentEvents {
    onClick?: (event: MouseEvent) => void;
    onMouseDown?: (event: MouseEvent) => void;
    onMouseUp?: (event: MouseEvent) => void;
    onMouseMove?: (event: MouseEvent) => void;
    onMouseEnter?: (event: MouseEvent) => void;
    onMouseLeave?: (event: MouseEvent) => void;
    onKeyDown?: (event: KeyboardEvent) => void;
    onKeyUp?: (event: KeyboardEvent) => void;
}

export interface UIComponentState {
    isHovered: boolean;
    isActive: boolean;
    isDragging: boolean;
}

export abstract class UIComponent {
    protected props: UIComponentProps;
    protected events: UIComponentEvents;
    protected state: UIComponentState;
    protected children: UIComponent[];
    protected parent?: UIComponent;

    constructor(props: UIComponentProps, events?: UIComponentEvents) {
        this.props = props;
        this.events = events || {};
        this.state = {
            isHovered: false,
            isActive: false,
            isDragging: false
        };
        this.children = [];
    }

    abstract draw(ctx: CanvasRenderingContext2D): void;

    update(deltaTime: number): void {
        // Update component logic
    }

    addChild(component: UIComponent): void {
        this.children.push(component);
        component.parent = this;
    }

    removeChild(component: UIComponent): void {
        const index = this.children.indexOf(component);
        if (index !== -1) {
            this.children.splice(index, 1);
            component.parent = undefined;
        }
    }

    handleEvent(event: Event): void {
        // Handle events based on event type
    }

    protected fireEvent(eventType: keyof UIComponentEvents, event: Event): void {
        const handler = this.events[eventType];
        if (handler) {
            handler(event as any);
        }
    }

    protected updateState(newState: Partial<UIComponentState>): void {
        this.state = { ...this.state, ...newState };
    }

    protected calculateBounds(): DOMRect {
        // Calculate component bounds
        return new DOMRect(this.props.x, this.props.y, this.props.width, this.props.height);
    }

    protected isPointInside(x: number, y: number): boolean {
        const bounds = this.calculateBounds();
        return (
            x >= bounds.x &&
            x <= bounds.x + bounds.width &&
            y >= bounds.y &&
            y <= bounds.y + bounds.height
        );
    }
}
