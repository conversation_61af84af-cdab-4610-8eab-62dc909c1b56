<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSDF文本渲染演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
        }
        
        canvas {
            border: 1px solid #333;
            background: #000;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
        }
        
        .controls {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .control-row {
            display: flex;
            align-items: center;
            margin: 15px 0;
            gap: 15px;
        }
        
        .control-row label {
            min-width: 120px;
            color: #ccc;
            font-weight: bold;
        }
        
        .control-row input[type="text"] {
            flex: 1;
            max-width: 300px;
            padding: 8px 12px;
            background: #333;
            border: 1px solid #555;
            color: white;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .control-row input[type="range"] {
            flex: 1;
            max-width: 200px;
        }
        
        .control-row input[type="color"] {
            width: 50px;
            height: 35px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .control-row .value {
            min-width: 80px;
            color: #fff;
            font-weight: bold;
            font-family: monospace;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .info {
            background: #333;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            line-height: 1.6;
        }
        
        .info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .status {
            background: #444;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            border-left: 4px solid #4CAF50;
        }
        
        .error {
            border-left-color: #f44336;
            background: #4a2c2c;
        }
        
        .char-list {
            background: #333;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 18px;
            text-align: center;
            letter-spacing: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MSDF中文文本渲染演示</h1>
        
        <div class="controls">
            <h3>渲染控制</h3>
            
            <div class="control-row">
                <label>文本内容:</label>
                <input type="text" id="textInput" value="你好世界" placeholder="输入要渲染的文本">
            </div>
            
            <div class="control-row">
                <label>字体大小:</label>
                <input type="range" id="fontSizeSlider" min="20" max="120" value="48">
                <span class="value" id="fontSizeValue">48px</span>
            </div>
            
            <div class="control-row">
                <label>X位置:</label>
                <input type="range" id="xSlider" min="0" max="400" value="50">
                <span class="value" id="xValue">50</span>
            </div>
            
            <div class="control-row">
                <label>Y位置:</label>
                <input type="range" id="ySlider" min="0" max="300" value="100">
                <span class="value" id="yValue">100</span>
            </div>
            
            <div class="control-row">
                <label>描边宽度:</label>
                <input type="range" id="outlineSlider" min="0" max="0.2" step="0.01" value="0.05">
                <span class="value" id="outlineValue">0.05</span>
            </div>
            
            <div class="control-row">
                <label>平滑度:</label>
                <input type="range" id="smoothnessSlider" min="0.01" max="0.15" step="0.01" value="0.05">
                <span class="value" id="smoothnessValue">0.05</span>
            </div>
            
            <div class="control-row">
                <label>文本颜色:</label>
                <input type="color" id="colorPicker" value="#ffffff">
            </div>
            
            <div class="button-group">
                <button onclick="updateRender()">更新渲染</button>
                <button onclick="resetControls()">重置参数</button>
                <button onclick="testAllChars()">测试所有字符</button>
            </div>
        </div>
        
        <canvas id="canvas" width="800" height="400"></canvas>
        
        <div class="status" id="status">正在初始化...</div>
        
        <div class="info">
            <h3>字体信息</h3>
            <div>支持的字符: <span class="char-list" id="charList">加载中...</span></div>
            <div>字体大小: <span id="fontInfo">-</span></div>
            <div>图集尺寸: <span id="atlasInfo">-</span></div>
            <div>距离场范围: <span id="distanceInfo">-</span></div>
        </div>
        
        <div class="info">
            <h3>使用说明</h3>
            <ul>
                <li>本演示基于 <code>gl/res/custom-msdf.json</code> 字体数据</li>
                <li>支持的字符: 你、好、世、界</li>
                <li>使用WebGL2和MSDF技术实现高质量文本渲染</li>
                <li>支持实时调整字体大小、位置、描边等参数</li>
                <li>函数式编程实现，无class依赖</li>
            </ul>
        </div>
    </div>

    <script type="module">
        // 导入MSDF渲染器
        import { initRenderContext, loadFontData, renderText } from './msdf-demo.js';
        
        let renderContext = null;
        let fontData = null;
        let isInitialized = false;
        
        // 状态更新函数
        function updateStatus(message, isError = false) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = isError ? 'status error' : 'status';
        }
        
        // 更新字体信息显示
        function updateFontInfo(data) {
            document.getElementById('charList').textContent = data.info.charset.join(' ');
            document.getElementById('fontInfo').textContent = `${data.info.size}px`;
            document.getElementById('atlasInfo').textContent = `${data.common.scaleW}x${data.common.scaleH}`;
            document.getElementById('distanceInfo').textContent = data.distanceField.distanceRange;
        }
        
        // 初始化
        async function init() {
            try {
                updateStatus('正在初始化WebGL2上下文...');
                
                const canvas = document.getElementById('canvas');
                renderContext = await initRenderContext(canvas);
                
                updateStatus('正在加载字体数据...');
                fontData = await loadFontData();
                
                updateFontInfo(fontData);
                updateStatus('初始化完成，可以开始渲染');
                
                isInitialized = true;
                
                // 初始渲染
                updateRender();
                
            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus(`初始化失败: ${error.message}`, true);
            }
        }
        
        // 更新渲染
        function updateRender() {
            if (!isInitialized) {
                updateStatus('渲染器未初始化', true);
                return;
            }
            
            try {
                const text = document.getElementById('textInput').value;
                const fontSize = parseInt(document.getElementById('fontSizeSlider').value);
                const x = parseInt(document.getElementById('xSlider').value);
                const y = parseInt(document.getElementById('ySlider').value);
                const outlineWidth = parseFloat(document.getElementById('outlineSlider').value);
                const smoothness = parseFloat(document.getElementById('smoothnessSlider').value);
                const colorHex = document.getElementById('colorPicker').value;
                
                // 转换颜色
                const color = [
                    parseInt(colorHex.substr(1, 2), 16) / 255,
                    parseInt(colorHex.substr(3, 2), 16) / 255,
                    parseInt(colorHex.substr(5, 2), 16) / 255
                ];
                
                renderText(
                    renderContext,
                    fontData,
                    text,
                    x,
                    y,
                    fontSize,
                    color,
                    [1, 0, 0], // 描边颜色
                    outlineWidth,
                    smoothness
                );
                
                updateStatus(`渲染完成: "${text}" (${fontSize}px)`);
                
            } catch (error) {
                console.error('渲染失败:', error);
                updateStatus(`渲染失败: ${error.message}`, true);
            }
        }
        
        // 重置控制参数
        function resetControls() {
            document.getElementById('textInput').value = '你好世界';
            document.getElementById('fontSizeSlider').value = 48;
            document.getElementById('xSlider').value = 50;
            document.getElementById('ySlider').value = 100;
            document.getElementById('outlineSlider').value = 0.05;
            document.getElementById('smoothnessSlider').value = 0.05;
            document.getElementById('colorPicker').value = '#ffffff';
            
            updateSliderValues();
            updateRender();
        }
        
        // 测试所有字符
        function testAllChars() {
            if (!fontData) return;
            
            const allChars = fontData.info.charset.join('');
            document.getElementById('textInput').value = allChars;
            updateRender();
        }
        
        // 更新滑块显示值
        function updateSliderValues() {
            document.getElementById('fontSizeValue').textContent = document.getElementById('fontSizeSlider').value + 'px';
            document.getElementById('xValue').textContent = document.getElementById('xSlider').value;
            document.getElementById('yValue').textContent = document.getElementById('ySlider').value;
            document.getElementById('outlineValue').textContent = document.getElementById('outlineSlider').value;
            document.getElementById('smoothnessValue').textContent = document.getElementById('smoothnessSlider').value;
        }
        
        // 事件监听
        document.getElementById('textInput').addEventListener('input', updateRender);
        document.getElementById('fontSizeSlider').addEventListener('input', () => {
            updateSliderValues();
            updateRender();
        });
        document.getElementById('xSlider').addEventListener('input', () => {
            updateSliderValues();
            updateRender();
        });
        document.getElementById('ySlider').addEventListener('input', () => {
            updateSliderValues();
            updateRender();
        });
        document.getElementById('outlineSlider').addEventListener('input', () => {
            updateSliderValues();
            updateRender();
        });
        document.getElementById('smoothnessSlider').addEventListener('input', () => {
            updateSliderValues();
            updateRender();
        });
        document.getElementById('colorPicker').addEventListener('input', updateRender);
        
        // 全局函数
        window.updateRender = updateRender;
        window.resetControls = resetControls;
        window.testAllChars = testAllChars;
        
        // 初始化滑块值显示
        updateSliderValues();
        
        // 启动应用
        init();
    </script>
</body>
</html>
