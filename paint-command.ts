// 基础命令定义
interface BaseDrawCommands {
  circle: (params: { x: number; y: number; radius: number }) => void
  rect: (params: { x: number; y: number; width: number; height: number }) => void
  line: (params: { startX: number; startY: number; endX: number; endY: number }) => void
}

type BaseCommandType = keyof BaseDrawCommands

// 类型工具 - 合并用户自定义命令
type MergeCommands<Base, User> = Base & {
  [K in keyof User]: User[K]
}

// 用户自定义命令接口（用户需要实现）
interface UserDrawCommands {
  // 用户在这里声明自定义命令
  // aa: (params: { name: string; value: number }) => void
  // triangle: (params: { points: [number, number][] }) => void
}

// 工厂函数 - 这是关键！使用工厂来保持类型信息
type AnyDrawFn = (...args: any[]) => any;

function createPaintContext<UserCommands extends Record<string, AnyDrawFn> = {}>(
  userCommands?: UserCommands
): PaintContextByCommand<UserCommands> {
  return new PaintContextByCommand(userCommands) as PaintContextByCommand<UserCommands>
}

class PaintContextByCommand<UserCommands extends Record<string, AnyDrawFn> = {}> {
  private drawCommands: Partial<
    MergeCommands<BaseDrawCommands, UserCommands>
  > = {}

  constructor(userCommands?: UserCommands) {
    // 初始化用户命令（如果提供）
    if (userCommands) {
      Object.entries(userCommands).forEach(([key, value]) => {
        this.drawCommands[key as keyof MergeCommands<BaseDrawCommands, UserCommands>] = 
          value as any
      })
    }
  }

  // 注册方法 - 现在支持用户自定义类型
  register<
    K extends keyof MergeCommands<BaseDrawCommands, UserCommands>
  >(
    type: K,
    drawFn: MergeCommands<BaseDrawCommands, UserCommands>[K]
  ): void {
    this.drawCommands[type] = drawFn
  }

  // 绘制方法 - 类型安全的参数
  draw<
    K extends keyof MergeCommands<BaseDrawCommands, UserCommands>
  >(
    type: K,
    params: Parameters<MergeCommands<BaseDrawCommands, UserCommands>[K]>[0]
  ): void {
    const drawFn = this.drawCommands[type]
    if (drawFn) {
      drawFn(params)
    } else {
      throw new Error(`Draw command '${String(type)}' not registered`)
    }
  }

  // 获取所有可用命令
  getAvailableCommands(): (keyof MergeCommands<BaseDrawCommands, UserCommands>)[] {
    return Object.keys(this.drawCommands) as any
  }
}
interface MyCustomCommands {
  aa: (params: { name: string; value: number; color?: string }) => void
  triangle: (params: { points: [number, number][]; fill: boolean }) => void
  [key: string]: AnyDrawFn
// }
//   [key: string]: Function
}

// 正确的使用方式 - 通过工厂函数
const paint = createPaintContext<MyCustomCommands>()

// 现在类型安全完全生效！
paint.register('circle', ({ x, y, radius }) => {
  console.log(`Circle: (${x}, ${y}), radius: ${radius}`)
})

paint.register('aa', ({ name, value, color }) => {
  console.log(`Custom aa: ${name} = ${value}${color ? `, color: ${color}` : ''}`)
})

paint.register('triangle', ({ points, fill }) => {
  console.log(`Triangle: ${points.length} points, fill: ${fill}`)
})

// 类型安全的调用
paint.draw('aa', { name: 'test', value: 123, color: 'red' }) // ✅
paint.draw('triangle', { points: [[0,0], [100,0], [50,100]], fill: true }) // ✅

// 编译时错误
// paint.draw('aa', { wrong: 'prop' }) // ❌ 缺少 name 和 value
// paint.draw('triangle', { points: 'invalid' }) // ❌ points 类型错误