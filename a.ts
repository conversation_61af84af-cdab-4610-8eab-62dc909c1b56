
  interface Modifier {
    validate(ui: UI, origin: (ui: UI) => Display | undefined): Display | undefined;
    // render?(ui: UI, ctx: RenderContext, origin: (ui: UI, ctx: RenderContext) => void): void;
    // hitTest?: (ui: UI, origin: HitTestFn2, p: Point, hitTest: HitTest) => HitTestResult;
  }

  type DisplayObject = Drawable | Display;
  class Display implements DrawInfo {
    backList?: DisplayObject[] = undefined;
    constructor(
      public ui: UI,
      public list: DisplayObject[],
      public rect: Rect,
      public mat: Matrix | undefined = undefined,
      public alpha = 1,
    ) {
      // console.log('display.version', index);
    }
    add(ui: DisplayObject, atBottom = false) {
      if (atBottom) {
        this.backList ? this.backList.push(ui) : (this.backList = [ui]);
      } else {
        this.list.push(ui);
      }
    }
    toLocalMatrix() {
      const layoutMatrix = this.ui.layoutMatrix,
        mat = this.mat;
      return layoutMatrix && mat ? multiply2x3_2(layoutMatrix, mat) : layoutMatrix || mat;
    }
    //清除当前绘制列表, 相当于不调用默认渲染
    render(info?: DrawInfo, flats: FlatUI[] = []) {
      const currentInfo = info ? mergeDrawInfo2(this, this.ui.layoutMatrix, info) : this;
      const each = (d: DisplayObject) => {
        if (d instanceof Display) {
          d.render(currentInfo, flats);
        } else {
          flats.push({ ui: d, fr: currentInfo });
        }
      };
      if (this.backList) {
        const list = this.backList;
        let i = list.length;
        while (i-- > 0) {
          each(list[i]);
        }
      }
      for (const d of this.list) {
        each(d);
      }
      return flats;
    }
  }

  function mergeDrawInfo2(info: DrawInfo, layoutMatrix?: Matrix, global?: DrawInfo): DrawInfo {
    if (!layoutMatrix && !global) {
      return info;
    }
    const mat =
      layoutMatrix && info.mat ? multiply2x3_2(layoutMatrix, info.mat) : layoutMatrix || info.mat;
    if (!global) {
      return {
        alpha: info.alpha,
        mat,
      };
    }
    return {
      alpha: info.alpha * global.alpha,
      mat: mat && global.mat ? multiply2x3_2(global.mat, mat) : global.mat || mat,
    };
  }

  class ModifierBuilder {
    constructor(
      public validate = originValidate,
      public matrix = (ui: UI) => ui.matrix,
    ) {}
    background(color: Color) {
      return mergeModifier(this, background(color));
    }
    align(p: Position) {
      return mergeModifier(this, align(p));
    }
    padding(p) {
      return mergeModifier(this, padding(p));
    }
  }
  const Modifier = new ModifierBuilder();
  function mergeModifier(m0: ModifierBuilder, mInstance?: Modifier) {
    if (!mInstance) {
      return m0;
    }
    return new ModifierBuilder((ui) => {
      return mInstance.validate(ui, m0.validate);
    });
  }

  function originValidate(ui: UI) {
    return ui.applyValidate();
  }

  function background(color: Color): Modifier {
    return {
      validate(ui, origin) {
        const d = origin(ui);
        if (d) {
          const back = new RectUI(d.rect);
          back.fillStyle = color;
          d.add(back, true);
          return d;
        }
      },
    };
  }
  function padding(padding: InsetsOrNumber): Modifier | undefined {
    const insets = toInsetsArray(padding);
    if (isEmptyInsetsArray(insets)) {
      return;
    }
    return {
      validate(ui, origin) {
        const d = origin(ui);
        if (d) {
          d.rect = growRectByArray(d.rect, insets);
          return d;
        }
      },
    } as Modifier;
  }
  function align(p: RePosition): Modifier {
    const fn = toLocationFn(p);
    return {
      validate(ui: UI, origin) {
        const d = origin(ui);
        if (d) {
          const p = fn(d.rect);
          d.mat = d.mat ? translate(d.mat, -p.x, -p.y) : [1, 0, 0, 0, -p.x, -p.y];
        }
        return d;
      },
    };
  }
  let idIndex = 0;
  class UI {
    __id = ++idIndex;
    modifier?: ModifierBuilder;
    matrix?: Matrix = undefined;
    layoutMatrix?: Matrix = undefined;
    alpha = 1;

    __display?: Display = undefined;
    __flag = 1;
    validate() {
      if (this.__flag) {
        this.__flag = 0;
        return (this.__display = this.modifier
          ? this.modifier.validate(this)
          : this.applyValidate());
      }
      return this.__display;
    }
    applyValidate(): Display | undefined {
      const rect = this.measure();
      if (rect) {
        return new Display(this, isDrawable(this) ? [this] : [], rect, this.matrix, this.alpha);
      }
    }
    ///draw,measure
    measure(): Rect | void {}
  }
  type LayoutFn = (list: Display[]) => Rect;
  class ContainerUI extends UI {
    children?: UI[] = undefined;
    validDisplays?: Display[] = undefined;
    layoutFn?: LayoutFn = undefined;
    measure(): Rect | void {
      const list: Display[] = (this.validDisplays = []);
      this.children?.forEach((c) => {
        const d = c.validate();
        d && list.push(d);
      });
      this.layoutFn && this.layoutFn(list);
      return calculateBoundsBy(list);
    }
    applyValidate(): Display | undefined {
      const rect = this.measure();
      if (rect && this.validDisplays) {
        const list: DisplayObject[] = this.validDisplays.slice();
        return new Display(this, list, rect, this.matrix, this.alpha);
      }
    }
  }
  function calculateBoundsBy(list: Display[]) {
    const rects = list.map((d) => {
      const mat = d.toLocalMatrix();
      return mat ? transformRect(d.rect, mat) : d.rect;
    });
    return mergeRects(rects);
  }
  class ItemUI extends UI implements Drawable {
    draw(ctx: CanvasContext2D): void {
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('' + this.__id, 0, 0);
    }
    measure(): Rect | void {
      return { x: -10, y: -10, width: 20, height: 20 };
    }
  }