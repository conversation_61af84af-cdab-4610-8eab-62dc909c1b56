<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSDF测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        canvas {
            border: 1px solid #333;
            background: #000;
            display: block;
            margin: 20px auto;
        }
        
        .controls {
            text-align: center;
            margin: 20px;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            text-align: center;
            padding: 10px;
            background: #333;
            border-radius: 4px;
            margin: 10px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center;">MSDF文本渲染测试</h1>
    
    <canvas id="canvas" width="800" height="400"></canvas>
    
    <div class="controls">
        <button onclick="testBasic()">基础测试</button>
        <button onclick="testAllChars()">所有字符</button>
        <button onclick="testSizes()">不同大小</button>
        <button onclick="testOutline()">描边效果</button>
    </div>
    
    <div class="status" id="status">准备就绪</div>

    <script type="module">
        import { initRenderContext, loadFontData, renderText } from './msdf-demo.js';
        
        let context = null;
        let fontData = null;
        
        function updateStatus(msg) {
            document.getElementById('status').textContent = msg;
            console.log(msg);
        }
        
        async function init() {
            try {
                updateStatus('初始化WebGL2...');
                const canvas = document.getElementById('canvas');
                context = await initRenderContext(canvas);
                
                updateStatus('加载字体数据...');
                fontData = await loadFontData();
                
                updateStatus('初始化完成！支持字符: ' + fontData.info.charset.join(' '));
                
                // 默认渲染
                testBasic();
                
            } catch (error) {
                updateStatus('错误: ' + error.message);
                console.error(error);
            }
        }
        
        function testBasic() {
            if (!context || !fontData) return;
            
            renderText(
                context,
                fontData,
                '你好世界',
                50,
                100,
                48,
                [1, 1, 1],
                [0, 0, 0],
                0.05,
                0.05
            );
            
            updateStatus('基础测试: 你好世界');
        }
        
        function testAllChars() {
            if (!context || !fontData) return;
            
            const allChars = fontData.info.charset.join('');
            renderText(
                context,
                fontData,
                allChars,
                50,
                100,
                48,
                [0.8, 1, 0.8],
                [0, 0, 0],
                0.03,
                0.05
            );
            
            updateStatus('所有字符测试: ' + allChars);
        }
        
        function testSizes() {
            if (!context || !fontData) return;
            
            // 清除画布
            const gl = context.gl;
            gl.clearColor(0.1, 0.1, 0.1, 1);
            gl.clear(gl.COLOR_BUFFER_BIT);
            
            // 不同大小的文本
            const sizes = [24, 36, 48, 64];
            const colors = [
                [1, 0.8, 0.8],
                [0.8, 1, 0.8], 
                [0.8, 0.8, 1],
                [1, 1, 0.8]
            ];
            
            sizes.forEach((size, i) => {
                renderText(
                    context,
                    fontData,
                    '你好',
                    50,
                    50 + i * 80,
                    size,
                    colors[i],
                    [0, 0, 0],
                    0.05,
                    0.05
                );
            });
            
            updateStatus('不同大小测试: 24px, 36px, 48px, 64px');
        }
        
        function testOutline() {
            if (!context || !fontData) return;
            
            renderText(
                context,
                fontData,
                '世界',
                50,
                100,
                64,
                [1, 1, 0],
                [1, 0, 0],
                0.15,
                0.03
            );
            
            updateStatus('描边测试: 黄色文字 + 红色描边');
        }
        
        // 全局函数
        window.testBasic = testBasic;
        window.testAllChars = testAllChars;
        window.testSizes = testSizes;
        window.testOutline = testOutline;
        
        // 启动
        init();
    </script>
</body>
</html>
